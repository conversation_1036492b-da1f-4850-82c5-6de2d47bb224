import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { z } from 'zod'

// 密码修改验证schema
const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, '请输入当前密码'),
  newPassword: z
    .string()
    .min(8, '新密码至少8个字符')
    .max(50, '新密码不能超过50个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/,
      '新密码必须包含大小写字母、数字和特殊字符'
    ),
})

// PUT /api/user/password - 修改密码
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const userId = session.user.id
    const body = await request.json()

    // 验证请求数据
    const validation = changePasswordSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const { currentPassword, newPassword } = validation.data

    // 获取用户当前密码哈希
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, password_hash: true, username: true },
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(
      currentPassword,
      user.password_hash
    )
    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { success: false, error: '当前密码不正确' },
        { status: 400 }
      )
    }

    // 检查新密码是否与当前密码相同
    const isSamePassword = await bcrypt.compare(newPassword, user.password_hash)
    if (isSamePassword) {
      return NextResponse.json(
        { success: false, error: '新密码不能与当前密码相同' },
        { status: 400 }
      )
    }

    // 生成新密码哈希
    const saltRounds = 12
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds)

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password_hash: newPasswordHash,
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId,
        action: 'CHANGE_PASSWORD',
        resource: 'User',
        resourceId: userId.toString(),
        details: {
          username: user.username,
          timestamp: new Date().toISOString(),
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '密码修改成功',
    })
  } catch (error) {
    console.error('修改密码失败:', error)
    return NextResponse.json(
      { success: false, error: '修改密码失败' },
      { status: 500 }
    )
  }
}
