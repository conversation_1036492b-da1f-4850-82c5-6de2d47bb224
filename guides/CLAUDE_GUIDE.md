# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the "肺功能数据管理平台" (Lung Function Data Management Platform) - a modern full-stack web application built with Next.js for managing data from Jin<PERSON><PERSON> (金数据) webhook integrations. The platform provides user management, form configuration, data management, and export capabilities.

## Technology Stack

- **Framework**: Next.js 14 (App Router)
- **UI Library**: Ant Design 5.x (Blue theme)
- **Styling**: Tailwind CSS
- **Database**: Tencent Cloud Lightweight Database (MySQL) + Prisma ORM
- **Authentication**: NextAuth.js + bcrypt
- **State Management**: Zustand
- **Deployment**: Docker + Docker Compose

## Development Commands

### Local Development Setup

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev

# Start development server
npm run dev

# Open Prisma Studio (database GUI)
npx prisma studio
```

### Common Development Commands

```bash
# Build for production
npm run build

# Start production server
npm start

# Run type checking
npm run type-check

# Run linting
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Run tests
npm test

# Run tests in watch mode
npm test:watch
```

### Database Commands

```bash
# Create new migration
npx prisma migrate dev --name description

# Reset database
npx prisma migrate reset

# Push schema changes (development only)
npx prisma db push

# Seed database
npx prisma db seed
```

### Docker Commands

```bash
# Build and start containers
docker-compose up --build

# Start containers in background
docker-compose up -d

# Stop containers
docker-compose down

# View logs
docker-compose logs -f

# Rebuild specific service
docker-compose build web
```

## Project Architecture

### Directory Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Main application pages
│   ├── api/               # API routes
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── ui/               # Basic UI components
│   ├── forms/            # Form-related components
│   ├── data/             # Data display components
│   └── layout/           # Layout components
├── lib/                  # Utility libraries
├── types/                # TypeScript type definitions
└── store/                # Zustand stores
```

### Key Features

1. **User Authentication System** - Login with username/password, user management
2. **Form Configuration Management** - JSON-based form configuration, dynamic table creation
3. **Webhook Data Reception** - Receives Jinshuju webhook data automatically
4. **Data Management Interface** - CRUD operations, batch operations, advanced search
5. **Data Export** - Excel/CSV export with filtering
6. **System Logging** - Comprehensive logging system

## Database Configuration

### Environment Variables

```bash
# Development (External connection)
DATABASE_URL="mysql+pymysql://srmyy_123:<EMAIL>:23387/srmyy_123"

# Production (Internal connection)
DATABASE_URL="mysql+pymysql://srmyy_123:gg9gpaEqkg4s@*********:3306/srmyy_123"
```

### Database Schema

- `users` - User accounts and profiles
- `form_configs` - Form configuration and field mappings
- `form_data_{form_id}` - Dynamic tables for each form type
- `system_logs` - Application logs and audit trail

## API Routes

### Authentication

- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout
- `GET /api/auth/me` - Get current user

### Form Management

- `GET /api/forms` - List form configurations
- `POST /api/forms` - Create form configuration
- `PUT /api/forms/[id]` - Update form configuration
- `DELETE /api/forms/[id]` - Delete form configuration

### Data Management

- `GET /api/data/[formId]` - Get form data with pagination
- `POST /api/data/[formId]` - Create new data entry
- `PUT /api/data/[formId]/[id]` - Update data entry
- `DELETE /api/data/[formId]/[id]` - Delete data entry
- `GET /api/data/[formId]/export` - Export data

### Webhook Reception

- `POST /api/webhook/[formId]` - Receive Jinshuju webhook data

## Development Workflow

### Adding New Features

1. Update the relevant task in `DEVELOPMENT_PLAN.md` to `[~]` (in progress)
2. Create feature branch: `git checkout -b feature/description`
3. Implement the feature following the technical specifications
4. Test the feature thoroughly
5. Update documentation if needed
6. Mark task as completed `[x]` in `DEVELOPMENT_PLAN.md`
7. Create pull request or merge to main

### Form Configuration Process

1. Admin inputs sample JSON from Jinshuju
2. System parses JSON structure and extracts field mappings
3. Dynamic database table is created with appropriate columns
4. Webhook endpoint is automatically configured
5. Data starts flowing from Jinshuju to the platform

### Webhook Data Flow

1. Jinshuju sends POST request to `/api/webhook/[formId]`
2. System validates the request and parses JSON data
3. Data is transformed according to field mappings
4. Data is stored in the corresponding form data table
5. Operation is logged for audit purposes

## Code Style and Standards

### TypeScript

- Use strict type checking
- Define interfaces for all data structures
- Use proper error handling with try-catch blocks
- Follow naming conventions (PascalCase for components, camelCase for functions)

### Components

- Use functional components with hooks
- Implement proper error boundaries
- Follow Ant Design design patterns
- Ensure components are responsive

### API Routes

- Use consistent response formats
- Implement proper error handling
- Add request validation using Zod
- Include comprehensive logging

## Security Considerations

- All passwords are hashed using bcrypt
- API routes are protected with authentication middleware
- Input validation on all user inputs
- SQL injection protection through Prisma ORM
- XSS protection in React components
- CSRF protection for form submissions

## Performance Optimization

- Use Prisma's optimized queries with proper select statements
- Implement pagination for large datasets
- Use React.memo for expensive components
- Optimize images with Next.js Image component
- Enable compression and caching in production

## Deployment

### Production Deployment (腾讯云轻量服务器 + 腾讯云轻量数据库)

#### 方式一：简化部署（推荐）

```bash
# 停止当前容器
docker-compose down

# 使用简化配置部署（不包含MySQL）
docker-compose -f docker-compose.simple.yml up -d

# 查看运行状态
docker-compose -f docker-compose.simple.yml ps
```

#### 方式二：生产环境部署（含Nginx）

```bash
# 停止当前容器
docker-compose down

# 使用生产配置部署
docker-compose -f docker-compose.prod.yml up -d

# 查看运行状态
docker-compose -f docker-compose.prod.yml ps
```

#### 生产部署步骤

1. 确保 `.env` 文件配置正确（DATABASE_URL指向腾讯云数据库）
2. 选择合适的部署方式（simple或prod）
3. 构建并启动容器
4. 检查应用健康状态：`curl http://localhost:3000/api/health`
5. 监控日志：`docker-compose logs -f`

#### 注意事项

- **不要使用主 `docker-compose.yml`**，因为它包含MySQL服务会导致端口冲突
- 腾讯云轻量数据库使用外部连接，无需本地MySQL容器
- 生产环境使用 `docker-compose.prod.yml`（包含Nginx反向代理）
- 开发测试使用 `docker-compose.simple.yml`（仅应用服务）

### Environment Variables Required

- `DATABASE_URL` - Database connection string
- `NEXTAUTH_SECRET` - NextAuth.js secret key
- `NEXTAUTH_URL` - Application URL
- `JWT_SECRET` - JWT signing secret

## Troubleshooting

### Common Issues

1. **Database Connection Issues**: Check DATABASE_URL format and network connectivity
2. **Build Failures**: Ensure all dependencies are installed and TypeScript errors are resolved
3. **Authentication Problems**: Verify NEXTAUTH_SECRET is set and sessions are configured
4. **Webhook Reception**: Check form_id mapping and JSON parsing logic

### Debugging

- Check application logs in `logs/` directory
- Use Prisma Studio to inspect database state
- Enable debug logging by setting LOG_LEVEL=debug
- Use browser developer tools for frontend debugging

## Documentation References

- `PROJECT_DESIGN.md` - Complete product design and requirements
- `DEVELOPMENT_PLAN.md` - Detailed development tasks and progress
- `TECHNICAL_SPEC.md` - Technical architecture and specifications

## Development Status

Check `DEVELOPMENT_PLAN.md` for current development progress and task completion status. The project follows a 4-phase development approach with clear milestones and deliverables.
