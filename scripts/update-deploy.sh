#!/bin/bash

# 肺功能数据管理平台 - 快速更新部署脚本
# 适用于代码更新后的快速部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_DIR="$HOME/www/wwwroot/free_lung_function_project"
APP_NAME="lung-function-admin"
BACKUP_DIR="$HOME/backups/$(date +%Y%m%d_%H%M%S)"

# 检查项目目录
check_project() {
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先运行初始部署脚本: ./scripts/deploy-traditional.sh"
        exit 1
    fi
    
    cd "$PROJECT_DIR"
    log_success "项目目录检查完成"
}

# 备份当前版本
backup_current() {
    log_info "备份当前版本..."
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份关键文件
    cp -r .next "$BACKUP_DIR/" 2>/dev/null || log_warning "没有找到 .next 目录"
    cp -r node_modules "$BACKUP_DIR/" 2>/dev/null || log_warning "没有找到 node_modules 目录"
    cp .env.production "$BACKUP_DIR/" 2>/dev/null || log_warning "没有找到 .env.production 文件"
    cp package.json "$BACKUP_DIR/" 2>/dev/null || true
    cp yarn.lock "$BACKUP_DIR/" 2>/dev/null || true
    
    log_success "备份完成: $BACKUP_DIR"
}

# 拉取最新代码
pull_latest_code() {
    log_info "拉取最新代码..."
    
    # 检查是否有未提交的更改
    if ! git diff --quiet; then
        log_warning "检测到未提交的更改，正在暂存..."
        git stash push -m "Auto stash before update $(date)"
    fi
    
    # 获取当前分支
    current_branch=$(git branch --show-current)
    log_info "当前分支: $current_branch"
    
    # 拉取最新代码
    git fetch origin
    git pull origin "$current_branch"
    
    log_success "代码更新完成"
}

# 检查依赖更新
check_dependencies() {
    log_info "检查依赖更新..."
    
    # 比较package.json是否有变化
    if [ -f "$BACKUP_DIR/package.json" ]; then
        if ! diff -q package.json "$BACKUP_DIR/package.json" > /dev/null; then
            log_warning "检测到依赖更新，将重新安装..."
            return 0
        else
            log_info "依赖无变化，跳过安装"
            return 1
        fi
    else
        log_info "强制重新安装依赖"
        return 0
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 使用缓存的node_modules（如果package.json没变）
    if check_dependencies; then
        yarn install --frozen-lockfile
    else
        log_info "使用缓存的依赖"
    fi
    
    log_success "依赖安装完成"
}

# 数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 生成Prisma客户端
    npx prisma generate
    
    # 运行迁移（如果有新的迁移文件）
    if [ -d "prisma/migrations" ] && [ "$(ls -A prisma/migrations)" ]; then
        log_info "发现迁移文件，正在执行..."
        npx prisma migrate deploy
    else
        log_info "使用db push同步数据库..."
        npx prisma db push
    fi
    
    log_success "数据库迁移完成"
}

# 构建应用
build_application() {
    log_info "构建生产版本..."
    
    # 清理之前的构建
    rm -rf .next
    
    # 构建应用
    NODE_ENV=production yarn build
    
    log_success "应用构建完成"
}

# 重启服务
restart_service() {
    log_info "重启应用服务..."
    
    # 重启PM2应用
    if pm2 list | grep -q "$APP_NAME"; then
        pm2 restart "$APP_NAME"
        
        # 等待服务启动
        sleep 5
        
        # 检查服务状态
        if pm2 list | grep "$APP_NAME" | grep -q "online"; then
            log_success "应用重启成功"
        else
            log_error "应用重启失败"
            pm2 logs "$APP_NAME" --lines 20
            exit 1
        fi
    else
        log_warning "PM2应用不存在，尝试启动..."
        pm2 start ecosystem.config.js --env production
    fi
    
    log_success "服务重启完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待应用完全启动
    sleep 10
    
    # 检查应用是否响应
    if curl -f -s http://localhost:3011/api/health > /dev/null; then
        log_success "应用健康检查通过"
    else
        log_error "应用健康检查失败"
        log_info "查看应用日志："
        pm2 logs "$APP_NAME" --lines 20
        
        # 提供回滚选项
        echo
        log_warning "是否需要回滚到上一版本？(y/n)"
        read -r rollback_choice
        if [ "$rollback_choice" = "y" ] || [ "$rollback_choice" = "Y" ]; then
            rollback_version
        fi
        exit 1
    fi
}

# 回滚版本
rollback_version() {
    log_warning "开始回滚到上一版本..."
    
    if [ -d "$BACKUP_DIR" ]; then
        # 停止当前服务
        pm2 stop "$APP_NAME" 2>/dev/null || true
        
        # 恢复文件
        cp -r "$BACKUP_DIR/.next" . 2>/dev/null || true
        cp -r "$BACKUP_DIR/node_modules" . 2>/dev/null || true
        cp "$BACKUP_DIR/.env.production" . 2>/dev/null || true
        
        # 重启服务
        pm2 start "$APP_NAME"
        
        log_success "回滚完成"
    else
        log_error "没有找到备份文件，无法回滚"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份..."
    
    # 只保留最近5个备份
    if [ -d "$HOME/backups" ]; then
        cd "$HOME/backups"
        ls -t | tail -n +6 | xargs rm -rf 2>/dev/null || true
    fi
    
    log_success "备份清理完成"
}

# 显示更新信息
show_update_info() {
    log_success "=== 更新部署完成 ==="
    echo
    log_info "部署信息:"
    echo "  - 项目路径: $PROJECT_DIR"
    echo "  - 备份路径: $BACKUP_DIR"
    echo "  - 应用地址: http://$(curl -s ifconfig.me 2>/dev/null || echo 'YOUR_SERVER_IP')"
    echo "  - 本地地址: http://localhost:3011"
    echo
    log_info "管理命令:"
    echo "  - 查看状态: pm2 status"
    echo "  - 查看日志: pm2 logs $APP_NAME"
    echo "  - 重启应用: pm2 restart $APP_NAME"
    echo
    log_info "Git信息:"
    echo "  - 当前分支: $(git branch --show-current)"
    echo "  - 最新提交: $(git log --oneline -1)"
}

# 主函数
main() {
    log_info "开始快速更新部署..."
    
    check_project
    backup_current
    pull_latest_code
    install_dependencies
    run_migrations
    build_application
    restart_service
    health_check
    cleanup_old_backups
    show_update_info
    
    log_success "快速更新部署完成！"
}

# 处理命令行参数
case "${1:-}" in
    --rollback)
        log_info "执行回滚操作..."
        rollback_version
        exit 0
        ;;
    --no-backup)
        log_info "跳过备份步骤"
        main_no_backup() {
            check_project
            pull_latest_code
            install_dependencies
            run_migrations
            build_application
            restart_service
            health_check
            show_update_info
        }
        main_no_backup
        exit 0
        ;;
    --help|-h)
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  --rollback     回滚到上一版本"
        echo "  --no-backup    跳过备份步骤（更快但有风险）"
        echo "  --help, -h     显示帮助信息"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac