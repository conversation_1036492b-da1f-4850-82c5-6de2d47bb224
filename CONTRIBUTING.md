# 贡献指南

感谢您对肺功能数据管理平台的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、测试、反馈和建议。

## 📋 目录

- [贡献方式](#贡献方式)
- [开发环境设置](#开发环境设置)
- [代码规范](#代码规范)
- [提交流程](#提交流程)
- [Pull Request 指南](#pull-request-指南)
- [问题报告](#问题报告)
- [功能请求](#功能请求)
- [代码审查](#代码审查)
- [社区准则](#社区准则)

## 🤝 贡献方式

### 代码贡献

- 🐛 **Bug修复**: 修复已知问题和错误
- ✨ **新功能**: 添加新的功能特性
- ⚡ **性能优化**: 提升应用性能和效率
- 🔒 **安全增强**: 改进安全性和防护措施
- 🎨 **UI/UX改进**: 优化用户界面和体验

### 非代码贡献

- 📝 **文档改进**: 完善文档、教程和指南
- 🧪 **测试**: 编写和改进测试用例
- 🐛 **问题报告**: 报告Bug和问题
- 💡 **功能建议**: 提出新功能想法
- 🌍 **翻译**: 帮助项目国际化

## 🛠️ 开发环境设置

### 前置要求

- Node.js 18.0+
- npm 或 yarn
- Git
- MySQL 8.0+ (本地开发)

### 环境配置

1. **Fork 项目**

```bash
# 在GitHub上Fork项目到您的账户
# 然后克隆您的Fork
git clone https://github.com/YOUR_USERNAME/free_lung_function_project_admin.git
cd free_lung_function_project_admin
```

2. **添加上游仓库**

```bash
git remote add upstream https://github.com/peckbyte/free_lung_function_project_admin.git
```

3. **安装依赖**

```bash
npm install
# 或
yarn install
```

4. **配置环境变量**

```bash
cp .env.example .env.local
# 编辑 .env.local 配置数据库连接等信息
```

5. **初始化数据库**

```bash
npx prisma db push
```

6. **启动开发服务器**

```bash
npm run dev
```

### 推荐工具

- **IDE**: VS Code
- **必需插件**:
  - ESLint
  - Prettier
  - Tailwind CSS IntelliSense
  - Prisma
  - GitLens

## 📏 代码规范

### TypeScript 规范

- 使用严格模式 (`strict: true`)
- 为所有函数和变量提供类型注解
- 避免使用 `any` 类型
- 使用接口定义对象结构

```typescript
// ✅ 好的示例
interface UserData {
  id: string;
  name: string;
  email: string;
}

const createUser = (data: UserData): Promise<User> => {
  // 实现
};

// ❌ 避免的示例
const createUser = (data: any) => {
  // 实现
};
```

### React 组件规范

- 使用函数组件和 Hooks
- 组件名使用 PascalCase
- Props 接口以组件名 + Props 命名

```typescript
// ✅ 好的示例
interface UserCardProps {
  user: User;
  onEdit: (id: string) => void;
}

const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
  return (
    <div className="user-card">
      {/* 组件内容 */}
    </div>
  );
};
```

### CSS 规范

- 使用 Tailwind CSS 类名
- 避免内联样式
- 使用语义化的类名

```typescript
// ✅ 好的示例
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
  <h2 className="text-lg font-semibold text-gray-900">标题</h2>
</div>

// ❌ 避免的示例
<div style={{ display: 'flex', padding: '16px' }}>
  <h2 style={{ fontSize: '18px' }}>标题</h2>
</div>
```

### 命名规范

- **文件名**: kebab-case (例: `user-profile.tsx`)
- **组件名**: PascalCase (例: `UserProfile`)
- **函数名**: camelCase (例: `getUserData`)
- **常量**: UPPER_SNAKE_CASE (例: `MAX_FILE_SIZE`)
- **变量**: camelCase (例: `userData`)

### 注释规范

```typescript
/**
 * 创建新用户
 * @param userData - 用户数据
 * @returns Promise<User> - 创建的用户对象
 */
const createUser = async (userData: CreateUserData): Promise<User> => {
  // 验证用户数据
  validateUserData(userData);
  
  // 创建用户
  return await prisma.user.create({
    data: userData,
  });
};
```

## 🔄 提交流程

### 分支策略

- `main`: 主分支，包含稳定的生产代码
- `develop`: 开发分支，包含最新的开发代码
- `feature/*`: 功能分支，用于开发新功能
- `bugfix/*`: 修复分支，用于修复Bug
- `hotfix/*`: 热修复分支，用于紧急修复

### 提交信息规范

使用语义化提交信息 (Conventional Commits):

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型 (type)**:
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:
```
feat(auth): add password strength validation

- Add password complexity requirements
- Implement real-time validation feedback
- Update user registration form

Closes #123
```

### 开发流程

1. **创建功能分支**

```bash
git checkout -b feature/your-feature-name
```

2. **开发和测试**

```bash
# 开发您的功能
# 运行测试确保代码质量
npm run test
npm run lint
npm run type-check
```

3. **提交代码**

```bash
git add .
git commit -m "feat: add your feature description"
```

4. **同步上游更改**

```bash
git fetch upstream
git rebase upstream/main
```

5. **推送到您的Fork**

```bash
git push origin feature/your-feature-name
```

## 📝 Pull Request 指南

### 创建 Pull Request

1. 确保您的分支是基于最新的 `main` 分支
2. 运行所有测试并确保通过
3. 更新相关文档
4. 创建 Pull Request

### PR 标题和描述

**标题格式**:
```
[Type] Brief description of changes
```

**描述模板**:
```markdown
## 📋 变更概述
简要描述此PR的目的和变更内容。

## 🔧 变更类型
- [ ] Bug修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 性能优化
- [ ] 代码重构
- [ ] 其他: ___________

## 📝 详细说明
详细描述您的变更，包括：
- 解决了什么问题
- 如何解决的
- 为什么选择这种方案

## 🧪 测试
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成
- [ ] 添加了新的测试用例

## 📸 截图 (如适用)
如果涉及UI变更，请提供截图。

## 🔗 相关Issue
Closes #issue_number

## ✅ 检查清单
- [ ] 代码遵循项目规范
- [ ] 已添加必要的测试
- [ ] 文档已更新
- [ ] 变更日志已更新
- [ ] 所有CI检查通过
```

### 代码审查要求

- 至少需要一个维护者的审查
- 所有CI检查必须通过
- 解决所有审查意见
- 确保没有合并冲突

## 🐛 问题报告

### 报告Bug

使用GitHub Issues报告Bug，请包含：

1. **Bug描述**: 清晰简洁的描述
2. **复现步骤**: 详细的复现步骤
3. **预期行为**: 您期望发生什么
4. **实际行为**: 实际发生了什么
5. **环境信息**: 
   - 操作系统
   - 浏览器版本
   - Node.js版本
   - 应用版本
6. **截图**: 如果适用
7. **错误日志**: 相关的错误信息

### Bug报告模板

```markdown
## 🐛 Bug描述
简要描述Bug的现象。

## 🔄 复现步骤
1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## ✅ 预期行为
清晰简洁地描述您期望发生什么。

## ❌ 实际行为
清晰简洁地描述实际发生了什么。

## 🖼️ 截图
如果适用，添加截图来帮助解释您的问题。

## 🖥️ 环境信息
- OS: [e.g. macOS 12.0]
- Browser: [e.g. Chrome 95.0]
- Node.js: [e.g. 18.0.0]
- App Version: [e.g. 1.0.0]

## 📋 附加信息
添加任何其他关于问题的上下文信息。
```

## 💡 功能请求

### 提出新功能

1. 检查是否已有类似的功能请求
2. 使用GitHub Issues创建功能请求
3. 详细描述功能需求和使用场景
4. 说明为什么这个功能有价值

### 功能请求模板

```markdown
## 🚀 功能描述
清晰简洁地描述您想要的功能。

## 💭 动机
解释为什么这个功能对项目有价值。

## 📝 详细描述
详细描述功能的工作方式。

## 🎯 使用场景
描述具体的使用场景。

## 🔄 替代方案
描述您考虑过的任何替代解决方案或功能。

## 📋 附加信息
添加任何其他关于功能请求的上下文或截图。
```

## 👀 代码审查

### 审查者指南

- 关注代码质量、可读性和维护性
- 检查是否遵循项目规范
- 验证测试覆盖率
- 确保文档完整性
- 提供建设性的反馈

### 被审查者指南

- 及时回应审查意见
- 解释设计决策
- 接受建设性批评
- 根据反馈改进代码

## 🌟 社区准则

### 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境，无论：
- 性别、性别认同和表达
- 性取向
- 残疾
- 外貌
- 身体大小
- 种族
- 年龄
- 宗教

### 期望行为

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 不当行为

- 使用性化的语言或图像
- 人身攻击或政治攻击
- 公开或私下骚扰
- 未经明确许可发布他人的私人信息
- 其他在专业环境中可能被认为不当的行为

## 📞 获取帮助

如果您需要帮助或有疑问：

1. 📖 查看[文档](docs/)
2. 🔍 搜索现有的[Issues](../../issues)
3. 💬 参与[讨论](../../discussions)
4. 📧 联系维护者

## 🙏 致谢

感谢所有为项目做出贡献的开发者！您的贡献让这个项目变得更好。

---

**再次感谢您的贡献！** 🎉
