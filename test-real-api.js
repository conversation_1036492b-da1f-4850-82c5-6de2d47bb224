const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testRealAPI() {
  try {
    console.log('🔍 测试真实API端点...\n')

    // 1. 首先确认用户存在
    const user = await prisma.user.findFirst({
      where: { role: 'admin' }
    })

    if (!user) {
      console.log('❌ 没有找到管理员用户')
      return
    }

    console.log(`✅ 找到管理员用户: ${user.username} (ID: ${user.id})`)

    // 2. 模拟API调用 - 直接调用API逻辑
    const userId = user.id
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    console.log(`今日时间范围: ${today.toISOString()} 到 ${tomorrow.toISOString()}\n`)

    // 检查用户角色
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    })

    const isAdmin = currentUser?.role === 'admin'
    console.log(`用户角色: ${currentUser?.role}, 是否管理员: ${isAdmin}\n`)

    if (isAdmin) {
      console.log('=== 管理员统计数据 ===')
      
      // 用户统计
      const totalUsers = await prisma.user.count()
      const activeUsers = await prisma.user.count({ where: { is_active: true } })
      console.log(`✅ 用户统计: 总数 ${totalUsers}, 活跃 ${activeUsers}`)

      // 表单统计
      const totalForms = await prisma.formConfig.count()
      const activeForms = await prisma.formConfig.count({ where: { isActive: true } })
      console.log(`✅ 表单统计: 总数 ${totalForms}, 活跃 ${activeForms}`)

      // 数据统计 - 使用修复后的逻辑
      const totalRecords = await getTotalRecords()
      const todayRecords = await getTodayRecords(today, tomorrow)
      console.log(`✅ 数据统计: 总记录 ${totalRecords}, 今日新增 ${todayRecords}`)

      // Webhook统计
      const webhookSuccess = await prisma.systemLog.count({
        where: {
          action: 'WEBHOOK_RECEIVED',
          createdAt: { gte: today },
        },
      })
      const webhookErrors = await prisma.systemLog.count({
        where: {
          action: 'WEBHOOK_ERROR',
          createdAt: { gte: today },
        },
      })
      console.log(`✅ Webhook统计: 成功 ${webhookSuccess}, 错误 ${webhookErrors}`)

      const stats = {
        totalUsers,
        activeUsers,
        totalForms,
        activeForms,
        totalRecords,
        todayRecords,
        webhookSuccess,
        webhookErrors,
      }

      console.log('\n📊 最终统计结果:')
      console.log(JSON.stringify(stats, null, 2))

      // 验证数据是否正确
      if (totalRecords > 0) {
        console.log('\n🎉 成功！数据统计不再是0了！')
      } else {
        console.log('\n⚠️  数据统计仍然是0，需要进一步检查')
      }
    }

    console.log('\n🎉 API测试完成!')

  } catch (error) {
    console.error('❌ API测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 获取所有数据表的总记录数 - 使用修复后的逻辑
async function getTotalRecords() {
  try {
    const forms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true },
    })

    let total = 0
    for (const form of forms) {
      if (form.tableName) {
        const count = await getTableRecordCount(form.tableName)
        total += count
      }
    }

    return total
  } catch (error) {
    console.error('获取总记录数失败:', error)
    return 0
  }
}

// 获取今日新增记录数 - 使用修复后的逻辑
async function getTodayRecords(today, tomorrow) {
  try {
    const forms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true },
    })

    let total = 0
    for (const form of forms) {
      if (form.tableName) {
        const count = await getTableTodayCount(form.tableName, today, tomorrow)
        total += count
      }
    }

    return total
  } catch (error) {
    console.error('获取今日记录数失败:', error)
    return 0
  }
}

// 获取单个表的记录数 - 修复后的逻辑
async function getTableRecordCount(tableName) {
  try {
    // 先检查表是否存在
    const tableExists = await checkTableExists(tableName)
    if (!tableExists) {
      console.warn(`表 ${tableName} 不存在`)
      return 0
    }

    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM \`${tableName}\``
    )
    return Number(result[0]?.count || 0)
  } catch (error) {
    console.error(`获取表 ${tableName} 记录数失败:`, error)
    return 0
  }
}

// 获取单个表的今日记录数 - 修复后的逻辑
async function getTableTodayCount(tableName, today, tomorrow) {
  try {
    // 先检查表是否存在
    const tableExists = await checkTableExists(tableName)
    if (!tableExists) {
      console.warn(`表 ${tableName} 不存在`)
      return 0
    }

    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM \`${tableName}\` WHERE created_at >= ? AND created_at < ?`,
      today,
      tomorrow
    )
    return Number(result[0]?.count || 0)
  } catch (error) {
    console.error(`获取表 ${tableName} 今日记录数失败:`, error)
    return 0
  }
}

// 检查表是否存在
async function checkTableExists(tableName) {
  try {
    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?`,
      tableName
    )
    return Number(result[0]?.count || 0) > 0
  } catch (error) {
    console.error(`检查表 ${tableName} 是否存在失败:`, error)
    return false
  }
}

testRealAPI()
