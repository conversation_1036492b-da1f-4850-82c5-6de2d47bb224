"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var client_1 = require("@prisma/client");
var bcryptjs_1 = require("bcryptjs");
var prisma = new client_1.PrismaClient();
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var hashedPassword, adminUser, testHashedPassword, testUser, sampleFormConfig;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('开始数据库种子数据创建...');
                    return [4 /*yield*/, bcryptjs_1.default.hash('admin123456', 12)];
                case 1:
                    hashedPassword = _a.sent();
                    return [4 /*yield*/, prisma.user.upsert({
                            where: { username: 'admin' },
                            update: {},
                            create: {
                                username: 'admin',
                                email: '<EMAIL>',
                                password_hash: hashedPassword,
                                role: 'admin',
                                doctor_name: '系统管理员',
                                is_active: true,
                                created_at: new Date(),
                            },
                        })];
                case 2:
                    adminUser = _a.sent();
                    console.log('创建管理员用户:', adminUser);
                    return [4 /*yield*/, bcryptjs_1.default.hash('test123456', 12)];
                case 3:
                    testHashedPassword = _a.sent();
                    return [4 /*yield*/, prisma.user.upsert({
                            where: { username: 'test' },
                            update: {},
                            create: {
                                username: 'test',
                                email: '<EMAIL>',
                                password_hash: testHashedPassword,
                                role: 'user',
                                doctor_name: '测试用户',
                                is_active: true,
                                created_at: new Date(),
                            },
                        })];
                case 4:
                    testUser = _a.sent();
                    console.log('创建测试用户:', testUser);
                    return [4 /*yield*/, prisma.formConfig.upsert({
                            where: { formId: 'ZFs2eo' },
                            update: {},
                            create: {
                                formId: 'ZFs2eo',
                                formName: '预约免费肺功能检查、免费办理慢性病医保',
                                fieldMapping: {
                                    field_1: { name: '姓名', type: 'string' },
                                    field_2: { name: '选择项', type: 'string' },
                                    field_3: { name: '电话', type: 'string' },
                                    field_4: { name: '多选项', type: 'array' },
                                    field_6: { name: '数字', type: 'number' },
                                    field_7: { name: '其他选项', type: 'string' },
                                    x_field_1: { name: '文本内容', type: 'text' },
                                },
                                tableName: 'form_data_ZFs2eo',
                                tableCreated: false,
                                webhookUrl: '/api/webhook/ZFs2eo',
                                isActive: true,
                                createdById: adminUser.id,
                            },
                        })];
                case 5:
                    sampleFormConfig = _a.sent();
                    console.log('创建示例表单配置:', sampleFormConfig);
                    // 创建系统日志示例
                    return [4 /*yield*/, prisma.systemLog.create({
                            data: {
                                action: 'system_init',
                                resource: 'system',
                                resourceId: null,
                                details: {
                                    level: 'info',
                                    message: '系统初始化完成',
                                    version: '1.0.0',
                                },
                                userId: adminUser.id,
                                ipAddress: '127.0.0.1',
                                userAgent: 'System',
                            },
                        })];
                case 6:
                    // 创建系统日志示例
                    _a.sent();
                    console.log('数据库种子数据创建完成！');
                    return [2 /*return*/];
            }
        });
    });
}
main()
    .then(function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, prisma.$disconnect()];
            case 1:
                _a.sent();
                return [2 /*return*/];
        }
    });
}); })
    .catch(function (e) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                console.error('种子数据创建失败:', e);
                return [4 /*yield*/, prisma.$disconnect()];
            case 1:
                _a.sent();
                process.exit(1);
                return [2 /*return*/];
        }
    });
}); });
