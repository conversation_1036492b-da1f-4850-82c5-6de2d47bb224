'use client'

import { Card, Row, Col, Statistic, Table, Typography, Space } from 'antd'
import {
  DatabaseOutlined,
  FormOutlined,
  UserOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography

// 模拟数据
const mockStats = {
  totalForms: 12,
  totalRecords: 1248,
  totalUsers: 3,
  todayRecords: 28,
}

const mockRecentData = [
  {
    key: '1',
    formName: '预约免费肺功能检查',
    recordCount: 156,
    lastUpdate: '2024-01-15 14:30:00',
  },
  {
    key: '2',
    formName: '慢性病医保办理',
    recordCount: 89,
    lastUpdate: '2024-01-15 13:45:00',
  },
  {
    key: '3',
    formName: '患者信息登记',
    recordCount: 234,
    lastUpdate: '2024-01-15 12:20:00',
  },
]

const columns = [
  {
    title: '表单名称',
    dataIndex: 'formName',
    key: 'formName',
  },
  {
    title: '记录数量',
    dataIndex: 'recordCount',
    key: 'recordCount',
    render: (count: number) => (
      <Text className="font-medium">{count.toLocaleString()}</Text>
    ),
  },
  {
    title: '最后更新',
    dataIndex: 'lastUpdate',
    key: 'lastUpdate',
    render: (time: string) => <Text type="secondary">{time}</Text>,
  },
]

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <Title level={2} className="mb-2">
          仪表板
        </Title>
        <Text type="secondary">欢迎使用肺功能数据管理平台</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="表单总数"
              value={mockStats.totalForms}
              prefix={<FormOutlined className="text-blue-500" />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="数据记录"
              value={mockStats.totalRecords}
              prefix={<DatabaseOutlined className="text-green-500" />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="用户数量"
              value={mockStats.totalUsers}
              prefix={<UserOutlined className="text-purple-500" />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日新增"
              value={mockStats.todayRecords}
              prefix={<ClockCircleOutlined className="text-orange-500" />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 最近数据表格 */}
      <Card>
        <div className="mb-4">
          <Title level={4} className="mb-1">
            最近数据活动
          </Title>
          <Text type="secondary">查看最近的表单数据变化</Text>
        </div>
        <Table
          columns={columns}
          dataSource={mockRecentData}
          pagination={false}
          size="middle"
        />
      </Card>

      {/* 快速操作 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="快速操作" size="small">
            <Space direction="vertical" size="small" className="w-full">
              <div className="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                <Text>新建表单配置</Text>
                <Text type="secondary">→</Text>
              </div>
              <div className="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                <Text>查看数据记录</Text>
                <Text type="secondary">→</Text>
              </div>
              <div className="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                <Text>导出数据</Text>
                <Text type="secondary">→</Text>
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="系统状态" size="small">
            <Space direction="vertical" size="small" className="w-full">
              <div className="flex justify-between items-center">
                <Text>数据库连接</Text>
                <Text className="text-green-500">正常</Text>
              </div>
              <div className="flex justify-between items-center">
                <Text>Webhook服务</Text>
                <Text className="text-green-500">运行中</Text>
              </div>
              <div className="flex justify-between items-center">
                <Text>系统负载</Text>
                <Text className="text-blue-500">轻度</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}
