#!/bin/bash

# 修复CentOS 7仓库源脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 修复CentOS 7仓库源
fix_centos_repos() {
    log_info "修复CentOS 7仓库源..."
    
    # 备份原始仓库文件
    sudo mkdir -p /etc/yum.repos.d/backup
    sudo cp /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/ 2>/dev/null || true
    
    # 更新CentOS-Base.repo
    sudo tee /etc/yum.repos.d/CentOS-Base.repo > /dev/null << 'EOF'
[base]
name=CentOS-$releasever - Base
baseurl=https://vault.centos.org/centos/$releasever/os/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7

[updates]
name=CentOS-$releasever - Updates
baseurl=https://vault.centos.org/centos/$releasever/updates/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7

[extras]
name=CentOS-$releasever - Extras
baseurl=https://vault.centos.org/centos/$releasever/extras/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7

[centosplus]
name=CentOS-$releasever - Plus
baseurl=https://vault.centos.org/centos/$releasever/centosplus/$basearch/
gpgcheck=1
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-7
EOF

    # 禁用有问题的仓库
    sudo yum-config-manager --disable centos-sclo-rh 2>/dev/null || true
    sudo yum-config-manager --disable centos-sclo-sclo 2>/dev/null || true
    
    # 移除重复的EPEL仓库
    sudo rm -f /etc/yum.repos.d/epel.repo.rpmnew
    
    # 清理缓存
    sudo yum clean all
    sudo rm -rf /var/cache/yum/*
    
    # 重建缓存
    sudo yum makecache
    
    log_success "CentOS仓库源修复完成"
}

# 安装EPEL仓库
install_epel() {
    log_info "安装EPEL仓库..."
    
    if ! rpm -qa | grep -q epel-release; then
        sudo yum install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-7.noarch.rpm
    else
        log_info "EPEL仓库已安装"
    fi
    
    log_success "EPEL仓库安装完成"
}

# 测试仓库连接
test_repos() {
    log_info "测试仓库连接..."
    
    if sudo yum repolist enabled; then
        log_success "仓库连接正常"
    else
        log_error "仓库连接失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始修复CentOS 7仓库源..."
    
    fix_centos_repos
    install_epel
    test_repos
    
    log_success "仓库源修复完成！现在可以正常使用yum安装软件包了"
    echo
    log_info "可以继续运行部署脚本：./scripts/deploy-traditional.sh"
}

main "$@"