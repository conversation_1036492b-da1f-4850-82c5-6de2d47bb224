const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🔐 肺功能数据管理平台 - 用户管理工具');
    console.log('='.repeat(60));
    
    // 测试数据库连接
    console.log('📡 测试数据库连接...');
    await prisma.user.count();
    console.log('✅ 数据库连接成功\n');

    // 1. 列出所有用户
    console.log('📋 用户列表:');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        doctor_name: true,
        is_active: true,
        created_at: true
      },
      orderBy: { created_at: 'desc' }
    });

    console.log('-'.repeat(100));
    console.log('ID'.padEnd(5) + '用户名'.padEnd(15) + '医生姓名'.padEnd(15) + '邮箱'.padEnd(30) + '角色'.padEnd(10) + '状态'.padEnd(8) + '创建时间');
    console.log('-'.repeat(100));
    
    users.forEach(user => {
      const status = user.is_active ? '激活' : '禁用';
      const createdDate = user.created_at ? new Date(user.created_at).toLocaleDateString('zh-CN') : '未知';
      console.log(
        user.id.toString().padEnd(5) +
        user.username.padEnd(15) +
        (user.doctor_name || '').padEnd(15) +
        (user.email || '').padEnd(30) +
        (user.role || '').padEnd(10) +
        status.padEnd(8) +
        createdDate
      );
    });
    console.log('-'.repeat(100));
    console.log(`总共 ${users.length} 个用户\n`);

    // 2. 测试admin用户密码
    console.log('🧪 测试admin用户密码验证:');
    const adminUser = await prisma.user.findUnique({
      where: { username: 'admin' }
    });

    if (adminUser) {
      const testPasswords = ['admin123456', 'admin123', 'admin', '123456'];
      
      for (const testPwd of testPasswords) {
        const isValid = await bcrypt.compare(testPwd, adminUser.password_hash);
        console.log(`  密码 '${testPwd}': ${isValid ? '✅ 正确' : '❌ 错误'}`);
        if (isValid) {
          console.log(`  ✅ 当前admin密码是: ${testPwd}`);
          break;
        }
      }
    }

    console.log('\n📝 如需重置密码，请运行:');
    console.log('node -e "');
    console.log('const { PrismaClient } = require(\\\"@prisma/client\\\");');
    console.log('const bcrypt = require(\\\"bcryptjs\\\");');
    console.log('const prisma = new PrismaClient();');
    console.log('(async () => {');
    console.log('  const newPassword = \\\"your_new_password\\\";');
    console.log('  const hashedPassword = await bcrypt.hash(newPassword, 12);');
    console.log('  await prisma.user.update({');
    console.log('    where: { username: \\\"admin\\\" },');
    console.log('    data: { password_hash: hashedPassword }');
    console.log('  });');
    console.log('  console.log(\\\"密码重置成功!\\\");');
    console.log('  await prisma.$disconnect();');
    console.log('})();');
    console.log('"');

  } catch (error) {
    console.error('❌ 程序执行出错:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

main();