import { prisma } from './prisma'

export interface FieldMapping {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | 'text'
  required: boolean
  description?: string
}

export interface TableCreationResult {
  success: boolean
  tableName: string
  error?: string
}

/**
 * 根据字段映射创建动态数据表
 */
export async function createDynamicTable(
  formId: string,
  fieldMapping: Record<string, FieldMapping>
): Promise<TableCreationResult> {
  const tableName = `form_data_${formId}`

  try {
    // 构建SQL字段定义
    const fieldDefinitions: string[] = [
      'id BIGINT AUTO_INCREMENT PRIMARY KEY',
      'serial_number INT NOT NULL COMMENT "金数据序列号"',
      'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间"',
      'updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间"',
      'source_ip VARCHAR(45) NULL COMMENT "来源IP地址"',
      'creator_name VARCHAR(100) NULL COMMENT "创建者姓名"',
      'raw_data JSON NULL COMMENT "原始JSON数据"',
    ]

    // 为每个映射字段添加列定义
    Object.entries(fieldMapping).forEach(([key, config]) => {
      const sqlType = getSQLTypeFromFieldType(config.type)
      const nullable = config.required ? 'NOT NULL' : 'NULL'
      const comment = config.description
        ? `COMMENT "${config.description.replace(/"/g, '\\"')}"`
        : `COMMENT "${config.name}"`

      fieldDefinitions.push(`\`${key}\` ${sqlType} ${nullable} ${comment}`)
    })

    // 构建完整的CREATE TABLE语句
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS \`${tableName}\` (
        ${fieldDefinitions.join(',\n        ')},
        INDEX idx_serial_number (serial_number),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      COMMENT='表单数据表: ${formId}'
    `

    // 执行SQL语句创建表
    await prisma.$executeRawUnsafe(createTableSQL)

    console.log(`动态表 ${tableName} 创建成功`)

    return {
      success: true,
      tableName,
    }
  } catch (error) {
    console.error(`创建动态表 ${tableName} 失败:`, error)
    return {
      success: false,
      tableName,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 检查动态表是否存在
 */
export async function checkTableExists(tableName: string): Promise<boolean> {
  try {
    const result = await prisma.$queryRawUnsafe<Array<{ table_name: string }>>(
      `SELECT table_name FROM information_schema.tables 
       WHERE table_schema = DATABASE() AND table_name = ?`,
      tableName
    )
    return result.length > 0
  } catch (error) {
    console.error(`检查表 ${tableName} 是否存在失败:`, error)
    return false
  }
}

/**
 * 删除动态表
 */
export async function dropDynamicTable(tableName: string): Promise<boolean> {
  try {
    await prisma.$executeRawUnsafe(`DROP TABLE IF EXISTS \`${tableName}\``)
    console.log(`动态表 ${tableName} 删除成功`)
    return true
  } catch (error) {
    console.error(`删除动态表 ${tableName} 失败:`, error)
    return false
  }
}

/**
 * 检查并修复表结构中的数字字段类型
 */
export async function fixTableNumberFields(
  tableName: string,
  fieldMapping: Record<string, FieldMapping>
): Promise<{ success: boolean; error?: string }> {
  try {
    // 获取当前表结构
    const columns = await prisma.$queryRawUnsafe<
      Array<{
        COLUMN_NAME: string
        DATA_TYPE: string
        COLUMN_TYPE: string
      }>
    >(
      `SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE
       FROM information_schema.COLUMNS
       WHERE table_schema = DATABASE() AND table_name = ?`,
      tableName
    )

    const alterStatements: string[] = []

    // 检查需要修复的数字字段
    Object.entries(fieldMapping).forEach(([key, config]) => {
      if (config.type === 'number') {
        const column = columns.find(col => col.COLUMN_NAME === key)
        if (
          column &&
          (column.DATA_TYPE === 'int' ||
            column.DATA_TYPE === 'tinyint' ||
            column.DATA_TYPE === 'smallint')
        ) {
          alterStatements.push(
            `ALTER TABLE \`${tableName}\` MODIFY COLUMN \`${key}\` DECIMAL(20,6) NULL`
          )
          console.log(
            `将修复字段 ${key}: ${column.COLUMN_TYPE} -> DECIMAL(20,6)`
          )
        }
      }
    })

    // 执行修复
    for (const statement of alterStatements) {
      await prisma.$executeRawUnsafe(statement)
    }

    if (alterStatements.length > 0) {
      console.log(
        `表 ${tableName} 的 ${alterStatements.length} 个数字字段已修复`
      )
    }

    return { success: true }
  } catch (error) {
    console.error(`修复表 ${tableName} 数字字段失败:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 检查类型转换是否安全
 */
function isTypeConversionSafe(
  fromType: string,
  toType: string,
  columnName: string
): { safe: boolean; warning?: string } {
  // 获取标准化的类型名称
  const normalizeType = (type: string): string => {
    if (
      type.includes('decimal') ||
      type.includes('int') ||
      type.includes('double') ||
      type.includes('float')
    ) {
      return 'number'
    }
    if (
      type.includes('varchar') ||
      type.includes('text') ||
      type.includes('char')
    ) {
      return 'string'
    }
    if (type.includes('json')) {
      return 'json'
    }
    if (type.includes('datetime') || type.includes('timestamp')) {
      return 'date'
    }
    if (type.includes('boolean') || type.includes('tinyint(1)')) {
      return 'boolean'
    }
    return type.toLowerCase()
  }

  const normalizedFrom = normalizeType(fromType)
  const normalizedTo = normalizeType(toType)

  // 相同类型总是安全的
  if (normalizedFrom === normalizedTo) {
    return { safe: true }
  }

  // 定义安全的转换规则
  const safeConversions: Record<string, string[]> = {
    number: ['string'], // 数字可以安全转换为字符串
    boolean: ['string'], // 布尔值可以安全转换为字符串
    date: ['string'], // 日期可以安全转换为字符串
    json: ['string'], // JSON可以安全转换为字符串
  }

  // 检查是否为安全转换
  if (safeConversions[normalizedFrom]?.includes(normalizedTo)) {
    return {
      safe: true,
      warning: `字段 ${columnName} 将从 ${normalizedFrom} 类型转换为 ${normalizedTo} 类型，现有数据将被保留`,
    }
  }

  // 字符串到其他类型的转换需要谨慎
  if (normalizedFrom === 'string' && normalizedTo !== 'string') {
    return {
      safe: false,
      warning: `字段 ${columnName} 从字符串转换为 ${normalizedTo} 可能导致数据丢失或转换失败`,
    }
  }

  // 其他转换被认为是不安全的
  return {
    safe: false,
    warning: `字段 ${columnName} 从 ${normalizedFrom} 转换为 ${normalizedTo} 可能不安全`,
  }
}

/**
 * 更新动态表的字段结构
 */
export async function updateTableSchema(
  formId: string,
  oldFieldMapping: Record<string, FieldMapping>,
  newFieldMapping: Record<string, FieldMapping>
): Promise<{ success: boolean; error?: string; warnings?: string[] }> {
  const tableName = `form_data_${formId}`
  const warnings: string[] = []

  try {
    // 检查表是否存在
    const tableExists = await checkTableExists(tableName)
    if (!tableExists) {
      console.log(`表 ${tableName} 不存在，将创建新表`)
      const createResult = await createDynamicTable(formId, newFieldMapping)
      return {
        success: createResult.success,
        error: createResult.error,
        warnings: createResult.success ? [`创建了新表 ${tableName}`] : [],
      }
    }

    // 获取当前表结构
    const columns = await prisma.$queryRawUnsafe<
      Array<{
        COLUMN_NAME: string
        DATA_TYPE: string
        COLUMN_TYPE: string
        IS_NULLABLE: string
        COLUMN_COMMENT: string
      }>
    >(
      `SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE, COLUMN_COMMENT
       FROM information_schema.COLUMNS
       WHERE table_schema = DATABASE() AND table_name = ?
       ORDER BY ORDINAL_POSITION`,
      tableName
    )

    const alterStatements: string[] = []

    // 检查需要修改的字段
    Object.entries(newFieldMapping).forEach(([key, newConfig]) => {
      const oldConfig = oldFieldMapping[key]
      const existingColumn = columns.find(col => col.COLUMN_NAME === key)

      if (existingColumn) {
        // 字段存在，检查是否需要修改
        if (oldConfig && oldConfig.type !== newConfig.type) {
          // 类型发生变化，检查转换安全性
          const conversionCheck = isTypeConversionSafe(
            existingColumn.COLUMN_TYPE,
            getSQLTypeFromFieldType(newConfig.type),
            key
          )

          if (!conversionCheck.safe) {
            warnings.push(
              conversionCheck.warning || `字段 ${key} 类型转换可能不安全`
            )
          } else if (conversionCheck.warning) {
            warnings.push(conversionCheck.warning)
          }

          const newSqlType = getSQLTypeFromFieldType(newConfig.type)
          const nullable = newConfig.required ? 'NOT NULL' : 'NULL'
          const comment = newConfig.description
            ? `COMMENT "${newConfig.description.replace(/"/g, '\\"')}"`
            : `COMMENT "${newConfig.name}"`

          alterStatements.push(
            `ALTER TABLE \`${tableName}\` MODIFY COLUMN \`${key}\` ${newSqlType} ${nullable} ${comment}`
          )
          console.log(
            `将修改字段 ${key}: ${existingColumn.COLUMN_TYPE} -> ${newSqlType}`
          )
        } else if (
          oldConfig &&
          (oldConfig.required !== newConfig.required ||
            oldConfig.description !== newConfig.description)
        ) {
          // 仅更新约束或注释
          const newSqlType = getSQLTypeFromFieldType(newConfig.type)
          const nullable = newConfig.required ? 'NOT NULL' : 'NULL'
          const comment = newConfig.description
            ? `COMMENT "${newConfig.description.replace(/"/g, '\\"')}"`
            : `COMMENT "${newConfig.name}"`

          alterStatements.push(
            `ALTER TABLE \`${tableName}\` MODIFY COLUMN \`${key}\` ${newSqlType} ${nullable} ${comment}`
          )
          console.log(`将更新字段 ${key} 的约束或注释`)
        }
      } else {
        // 新字段，需要添加
        const newSqlType = getSQLTypeFromFieldType(newConfig.type)
        const nullable = newConfig.required ? 'NOT NULL DEFAULT ""' : 'NULL'
        const comment = newConfig.description
          ? `COMMENT "${newConfig.description.replace(/"/g, '\\"')}"`
          : `COMMENT "${newConfig.name}"`

        alterStatements.push(
          `ALTER TABLE \`${tableName}\` ADD COLUMN \`${key}\` ${newSqlType} ${nullable} ${comment}`
        )
        console.log(`将添加新字段 ${key}: ${newSqlType}`)
        warnings.push(`添加了新字段 ${key}`)
      }
    })

    // 检查是否有字段被删除（在生产环境中通常不删除字段，只是不再使用）
    Object.keys(oldFieldMapping).forEach(key => {
      if (
        !newFieldMapping[key] &&
        columns.find(col => col.COLUMN_NAME === key)
      ) {
        warnings.push(`字段 ${key} 已从配置中移除，但在数据库中保留`)
      }
    })

    // 执行所有修改语句
    for (const statement of alterStatements) {
      console.log(`执行SQL: ${statement}`)
      await prisma.$executeRawUnsafe(statement)
    }

    if (alterStatements.length > 0) {
      console.log(`表 ${tableName} 的 ${alterStatements.length} 个字段已更新`)
      warnings.push(`成功更新了 ${alterStatements.length} 个字段`)
    } else {
      console.log(`表 ${tableName} 无需更新`)
    }

    return {
      success: true,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  } catch (error) {
    console.error(`更新表 ${tableName} 结构失败:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  }
}

/**
 * 向动态表插入数据
 */
export async function insertDataToDynamicTable(
  tableName: string,
  data: Record<string, any>,
  fieldMapping: Record<string, FieldMapping>
): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    // 准备插入的数据
    const insertData: Record<string, any> = {
      serial_number: data.serial_number,
      source_ip: data.source_ip || null,
      creator_name: data.creator_name || null,
      raw_data: JSON.stringify(data),
    }

    // 根据字段映射转换数据
    Object.entries(fieldMapping).forEach(([key, config]) => {
      const value = data[key]
      const convertedValue = convertValueForDatabase(value, config.type)
      insertData[key] = convertedValue

      // 添加详细日志用于调试
      if (config.type === 'number' && convertedValue !== null) {
        console.log(
          `字段 ${key} (${config.type}): 原始值=${value}, 转换后=${convertedValue}, 类型=${typeof convertedValue}`
        )
      }
    })

    // 构建字段名和占位符
    const fieldNames = Object.keys(insertData)
      .map(name => `\`${name}\``)
      .join(', ')
    const placeholders = Object.keys(insertData)
      .map(() => '?')
      .join(', ')
    const values = Object.values(insertData)

    // 执行插入操作
    const insertSQL = `INSERT INTO \`${tableName}\` (${fieldNames}) VALUES (${placeholders})`

    console.log(`准备插入数据到表 ${tableName}:`, {
      fieldNames: fieldNames,
      values: values,
      sql: insertSQL,
    })

    const result = await prisma.$executeRawUnsafe(insertSQL, ...values)

    // 获取插入的ID
    const [{ insertId }] = await prisma.$queryRawUnsafe<
      Array<{ insertId: bigint }>
    >('SELECT LAST_INSERT_ID() as insertId')

    return {
      success: true,
      id: Number(insertId),
    }
  } catch (error) {
    console.error(`向动态表 ${tableName} 插入数据失败:`, error)

    // 提供更详细的错误信息
    let errorMessage = '未知错误'
    if (error instanceof Error) {
      errorMessage = error.message

      // 特殊处理数据范围错误
      if (error.message.includes('Out of range value')) {
        const match = error.message.match(/column '(\w+)'/)
        const columnName = match ? match[1] : '未知字段'
        errorMessage = `字段 ${columnName} 的值超出允许范围。请检查数据格式和大小。`
      }
    }

    return {
      success: false,
      error: errorMessage,
    }
  }
}

/**
 * 根据字段类型获取对应的SQL数据类型
 */
function getSQLTypeFromFieldType(fieldType: FieldMapping['type']): string {
  switch (fieldType) {
    case 'string':
      return 'VARCHAR(500)'
    case 'number':
      // 使用DECIMAL(20,6)以支持更大范围的数字和更高精度
      return 'DECIMAL(20,6)'
    case 'boolean':
      return 'BOOLEAN'
    case 'date':
      return 'DATETIME'
    case 'array':
      return 'JSON'
    case 'object':
      return 'JSON'
    case 'text':
      return 'TEXT'
    default:
      return 'VARCHAR(500)'
  }
}

/**
 * 根据字段类型转换值为数据库可存储的格式
 */
function convertValueForDatabase(
  value: any,
  fieldType: FieldMapping['type']
): any {
  if (value === null || value === undefined || value === '') {
    return null
  }

  switch (fieldType) {
    case 'string':
      return String(value)
    case 'number':
      // 处理数字类型，支持更大范围和更好的验证
      if (typeof value === 'number') {
        // 检查是否为有效数字
        if (isNaN(value) || !isFinite(value)) {
          console.warn(`无效的数字值: ${value}`)
          return null
        }
        return value
      }

      if (typeof value === 'string') {
        // 移除可能的空格和非数字字符（除了小数点和负号）
        const cleanValue = value.trim().replace(/[^\d.-]/g, '')
        if (cleanValue === '' || cleanValue === '-') {
          return null
        }

        const num = Number(cleanValue)
        if (isNaN(num) || !isFinite(num)) {
          console.warn(`无法转换为数字: ${value} -> ${cleanValue}`)
          return null
        }

        // 检查数字范围（DECIMAL(20,6)的范围）
        const maxValue = 99999999999999.999999
        const minValue = -99999999999999.999999
        if (num > maxValue || num < minValue) {
          console.warn(`数字超出范围: ${num}, 将设置为null`)
          return null
        }

        return num
      }

      // 其他类型尝试转换
      const num = Number(value)
      return isNaN(num) || !isFinite(num) ? null : num

    case 'boolean':
      if (typeof value === 'boolean') return value
      if (typeof value === 'string') {
        const lower = value.toLowerCase().trim()
        return lower === 'true' || lower === '1' || lower === 'yes'
      }
      return Boolean(value)

    case 'date':
      if (typeof value === 'string') {
        const date = new Date(value)
        return isNaN(date.getTime()) ? null : date
      }
      return value instanceof Date ? value : null

    case 'array':
    case 'object':
      return typeof value === 'object' ? JSON.stringify(value) : String(value)

    case 'text':
      return String(value)

    default:
      return String(value)
  }
}
