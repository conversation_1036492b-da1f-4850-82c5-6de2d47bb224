const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function finalVerification() {
  try {
    console.log('🎯 最终验证仪表板数据...\n')

    // 模拟仪表板API的完整逻辑
    const userId = 1 // admin用户
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // 检查用户角色
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    })

    const isAdmin = currentUser?.role === 'admin'

    if (isAdmin) {
      // 管理员可以看到全部统计
      const [
        totalUsers,
        activeUsers,
        totalForms,
        activeForms,
        totalRecords,
        todayRecords,
        webhookSuccess,
        webhookErrors,
      ] = await Promise.all([
        // 用户统计
        prisma.user.count(),
        prisma.user.count({ where: { is_active: true } }),

        // 表单统计
        prisma.formConfig.count(),
        prisma.formConfig.count({ where: { isActive: true } }),

        // 数据统计 - 需要动态查询所有表
        getTotalRecords(),
        getTodayRecords(today, tomorrow),

        // Webhook统计
        prisma.systemLog.count({
          where: {
            action: 'WEBHOOK_RECEIVED',
            createdAt: { gte: today },
          },
        }),
        prisma.systemLog.count({
          where: {
            action: 'WEBHOOK_ERROR',
            createdAt: { gte: today },
          },
        }),
      ])

      const stats = {
        totalUsers,
        activeUsers,
        totalForms,
        activeForms,
        totalRecords,
        todayRecords,
        webhookSuccess,
        webhookErrors,
      }

      console.log('📊 仪表板统计数据:')
      console.log(`  👥 用户总数: ${totalUsers}`)
      console.log(`  ✅ 活跃用户: ${activeUsers}`)
      console.log(`  📋 表单总数: ${totalForms}`)
      console.log(`  🟢 活跃表单: ${activeForms}`)
      console.log(`  📊 数据总量: ${totalRecords}`)
      console.log(`  📈 今日新增: ${todayRecords}`)
      console.log(`  ✅ Webhook成功: ${webhookSuccess}`)
      console.log(`  ❌ Webhook错误: ${webhookErrors}`)

      // 验证结果
      console.log('\n🔍 验证结果:')
      
      const issues = []
      
      if (totalUsers === 0) issues.push('用户总数为0')
      if (activeUsers === 0) issues.push('活跃用户为0')
      if (totalForms === 0) issues.push('表单总数为0')
      if (activeForms === 0) issues.push('活跃表单为0')
      
      // 数据量可以为0，这是正常的
      
      if (issues.length === 0) {
        console.log('✅ 所有关键指标都正常！')
        console.log('✅ 仪表板应该能够正确显示数据')
      } else {
        console.log('❌ 发现问题:')
        issues.forEach(issue => console.log(`  - ${issue}`))
      }

      // 获取最近活动
      const recentActivities = await prisma.systemLog.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              username: true,
              doctor_name: true,
            },
          },
        },
      })

      console.log(`\n📝 最近活动 (${recentActivities.length} 条):`)
      recentActivities.forEach((log, index) => {
        const userName = log.user?.username || '系统'
        console.log(`  ${index + 1}. ${log.action} - ${log.resource} - ${userName} - ${log.createdAt.toLocaleString()}`)
      })

      // 获取表单统计
      const forms = await prisma.formConfig.findMany({
        where: { isActive: true },
        select: {
          formId: true,
          formName: true,
          tableName: true,
          isActive: true,
        },
      })

      console.log(`\n📋 活跃表单统计 (${forms.length} 个):`)
      
      for (const form of forms) {
        const recordCount = await getTableRecordCount(form.tableName)
        const todayCount = await getTableTodayCount(form.tableName, today, tomorrow)
        
        console.log(`  📊 ${form.formName}:`)
        console.log(`     - 总记录: ${recordCount}`)
        console.log(`     - 今日新增: ${todayCount}`)
      }

      console.log('\n🎉 验证完成！仪表板数据已修复！')
    }

  } catch (error) {
    console.error('❌ 验证失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 获取所有数据表的总记录数
async function getTotalRecords() {
  try {
    const forms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true },
    })

    let total = 0
    for (const form of forms) {
      if (form.tableName) {
        const count = await getTableRecordCount(form.tableName)
        total += count
      }
    }

    return total
  } catch (error) {
    console.error('获取总记录数失败:', error)
    return 0
  }
}

// 获取今日新增记录数
async function getTodayRecords(today, tomorrow) {
  try {
    const forms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true },
    })

    let total = 0
    for (const form of forms) {
      if (form.tableName) {
        const count = await getTableTodayCount(form.tableName, today, tomorrow)
        total += count
      }
    }

    return total
  } catch (error) {
    console.error('获取今日记录数失败:', error)
    return 0
  }
}

// 获取单个表的记录数
async function getTableRecordCount(tableName) {
  try {
    // 先检查表是否存在
    const tableExists = await checkTableExists(tableName)
    if (!tableExists) {
      return 0
    }

    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM \`${tableName}\``
    )
    return Number(result[0]?.count || 0)
  } catch (error) {
    console.error(`获取表 ${tableName} 记录数失败:`, error)
    return 0
  }
}

// 获取单个表的今日记录数
async function getTableTodayCount(tableName, today, tomorrow) {
  try {
    // 先检查表是否存在
    const tableExists = await checkTableExists(tableName)
    if (!tableExists) {
      return 0
    }

    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM \`${tableName}\` WHERE created_at >= ? AND created_at < ?`,
      today,
      tomorrow
    )
    return Number(result[0]?.count || 0)
  } catch (error) {
    console.error(`获取表 ${tableName} 今日记录数失败:`, error)
    return 0
  }
}

// 检查表是否存在
async function checkTableExists(tableName) {
  try {
    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?`,
      tableName
    )
    return Number(result[0]?.count || 0) > 0
  } catch (error) {
    console.error(`检查表 ${tableName} 是否存在失败:`, error)
    return false
  }
}

finalVerification()
