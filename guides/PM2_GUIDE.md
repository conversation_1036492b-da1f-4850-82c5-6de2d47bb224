# PM2 进程管理指南

本项目提供了完整的PM2配置和管理方案，支持开发、生产和集群模式。

## 🚀 快速开始

### 1. 基础使用

```bash
# 启动生产环境
pm2 start ecosystem.config.js --env production

# 启动集群模式
pm2 start ecosystem.config.js --env production-cluster

# 查看状态
pm2 status

# 查看日志
pm2 logs lung-function-admin
```

### 2. 使用管理脚本

```bash
# 给脚本执行权限
chmod +x scripts/pm2-manager.sh

# 启动应用
./scripts/pm2-manager.sh start production

# 查看所有可用命令
./scripts/pm2-manager.sh help
```

## 📋 配置说明

### 环境配置

| 环境 | 命令 | 说明 |
|------|------|------|
| `development` | `--env development` | 开发环境，单实例，端口3011 |
| `production` | `--env production` | 生产环境，单实例，端口3011 |
| `production-cluster` | `--env production-cluster` | 生产环境，集群模式，端口3011 |

### 关键特性

#### 1. 环境文件加载
```javascript
// 使用 node_args 加载环境文件（推荐方式）
node_args: '--env-file=.env.production'
```

#### 2. 多环境支持
```javascript
env_production: {
  NODE_ENV: 'production',
  PORT: 3011,
},
env_development: {
  NODE_ENV: 'development',
  PORT: 3011,
}
```

#### 3. 健康检查
```javascript
health_check_url: 'http://localhost:3011/api/health',
health_check_grace_period: 3000,
```

#### 4. 日志管理
```javascript
log_type: 'json',                // 结构化日志
log_file_max_size: '10M',        // 日志文件大小限制
log_file_max_files: 5,           // 保留日志文件数量
```

## 🛠️ 常用命令

### 应用管理
```bash
# 启动应用
./scripts/pm2-manager.sh start production

# 重启应用
./scripts/pm2-manager.sh restart production

# 零停机重载
./scripts/pm2-manager.sh reload production

# 停止应用
./scripts/pm2-manager.sh stop

# 删除应用
./scripts/pm2-manager.sh delete
```

### 监控和日志
```bash
# 查看状态
./scripts/pm2-manager.sh status

# 查看实时日志
./scripts/pm2-manager.sh logs

# 查看错误日志
./scripts/pm2-manager.sh logs-error

# 监控界面
./scripts/pm2-manager.sh monit

# 健康检查
./scripts/pm2-manager.sh health
```

### 配置管理
```bash
# 保存当前配置
./scripts/pm2-manager.sh save

# 配置开机自启
./scripts/pm2-manager.sh startup

# 备份配置和日志
./scripts/pm2-manager.sh backup
```

## 🔧 高级配置

### 集群模式

```bash
# 启动集群模式（使用所有CPU核心）
./scripts/pm2-manager.sh start production-cluster
```

集群模式配置：
```javascript
instances: 'max',        // 使用所有CPU核心
exec_mode: 'cluster',    // 集群模式
```

### 内存管理

```javascript
max_memory_restart: '1G',        // 内存限制重启
node_args: [
  '--max-old-space-size=1024',   // 最大堆内存
  '--optimize-for-size',         // 优化内存使用
].join(' ')
```

### 错误处理

```javascript
max_restarts: 10,                    // 最大重启次数
min_uptime: '10s',                   // 最小运行时间
exp_backoff_restart_delay: 100,      // 指数退避重启延迟
```

## 🚀 部署配置

### 远程部署

```bash
# 部署到生产环境
./scripts/pm2-manager.sh deploy production

# 或使用PM2原生命令
pm2 deploy ecosystem.config.js production
```

### 部署流程

1. **代码拉取**：从Git仓库拉取最新代码
2. **依赖安装**：`yarn install`
3. **数据库迁移**：`npx prisma generate && npx prisma db push`
4. **应用构建**：`yarn build`
5. **应用重载**：`pm2 reload`

## 📊 监控和维护

### 性能监控

```bash
# 查看应用详细信息
pm2 show lung-function-admin

# 查看资源使用情况
pm2 monit

# 查看进程列表
pm2 list
```

### 日志管理

```bash
# 查看实时日志
pm2 logs lung-function-admin --lines 100

# 清空日志
pm2 flush lung-function-admin

# 日志文件位置
ls -la logs/
```

### 备份和恢复

```bash
# 创建备份
./scripts/pm2-manager.sh backup

# 保存当前进程列表
pm2 save

# 恢复进程列表
pm2 resurrect
```

## 🔍 故障排除

### 常见问题

1. **应用无法启动**
   ```bash
   # 查看错误日志
   pm2 logs lung-function-admin --err
   
   # 检查配置文件
   pm2 show lung-function-admin
   ```

2. **内存泄漏**
   ```bash
   # 查看内存使用
   pm2 monit
   
   # 重启应用
   pm2 restart lung-function-admin
   ```

3. **端口占用**
   ```bash
   # 检查端口占用
   lsof -i :3011
   
   # 修改端口配置
   nano .env.production
   ```

### 调试模式

```bash
# 开发环境启动（更详细的日志）
pm2 start ecosystem.config.js --env development

# 查看详细日志
pm2 logs lung-function-admin --lines 200
```

## 📚 参考资料

- [PM2 官方文档](https://pm2.keymetrics.io/docs/)
- [PM2 生态系统文件](https://pm2.keymetrics.io/docs/usage/application-declaration/)
- [PM2 部署指南](https://pm2.keymetrics.io/docs/usage/deployment/)
- [Node.js 环境变量](https://nodejs.org/api/cli.html#--env-fileconfig)

## 🤝 贡献

如果您发现配置问题或有改进建议，请：

1. 查看现有的 Issues
2. 创建新的 Issue 描述问题
3. 提交 Pull Request

---

**注意**：生产环境部署前请确保：
- 已正确配置 `.env.production` 文件
- 数据库连接正常
- 防火墙已开放必要端口
- SSL证书已配置（如需要）
