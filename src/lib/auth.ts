import { NextAuthOptions } from 'next-auth'
import Credentials<PERSON>rovider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import type { User } from '@/types'

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: { label: '用户名', type: 'text' },
        password: { label: '密码', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          console.log('🔒 认证失败: 缺少用户名或密码')
          return null
        }

        try {
          console.log(`🔍 查找用户: ${credentials.username}`)
          // 查找用户
          const user = await prisma.user.findUnique({
            where: {
              username: credentials.username,
            },
          })

          if (!user) {
            console.log(`❌ 用户不存在: ${credentials.username}`)
            return null
          }

          if (!user.is_active) {
            console.log(`❌ 用户未激活: ${credentials.username}`)
            return null
          }

          console.log(`✅ 用户找到: ${user.username}, ID: ${user.id}`)
          console.log(`🔑 开始验证密码...`)
          console.log(`🔐 密码哈希: ${user.password_hash.substring(0, 20)}...`)

          // 验证密码
          const isValidPassword = await bcrypt.compare(
            credentials.password,
            user.password_hash
          )

          console.log(`🔓 密码验证结果: ${isValidPassword ? '通过' : '失败'}`)

          if (!isValidPassword) {
            console.log(`❌ 密码验证失败: ${credentials.username}`)
            return null
          }

          console.log(`🎉 密码验证成功，开始更新用户信息...`)

          // 更新最后登录时间 (注意：user表没有loginCount字段)
          await prisma.user.update({
            where: { id: user.id },
            data: {
              created_at: new Date(), // 更新时间戳
            },
          })

          // 记录登录日志
          await prisma.systemLog.create({
            data: {
              userId: user.id,
              action: 'LOGIN',
              resource: 'User',
              resourceId: user.id.toString(),
              details: {
                username: user.username,
                doctor_name: user.doctor_name,
              },
              ipAddress: 'unknown',
              userAgent: 'unknown',
            },
          })

          console.log(`✅ 登录成功: ${user.username}`)

          // 返回用户信息（不包含密码）
          return {
            id: user.id,
            username: user.username,
            nickname: user.doctor_name || undefined,
            email: user.email || undefined,
            avatarUrl: undefined,
            role: user.role || 'user',
            isActive: user.is_active || false,
          }
        } catch (error) {
          console.error('❌ 认证错误:', error)
          return null
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 天
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 天
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as number
        token.username = user.username
        token.nickname = user.nickname
        token.email = user.email
        token.avatarUrl = user.avatarUrl
        token.isActive = user.isActive
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      session.user = {
        id: Number(token.id),
        username: token.username as string,
        nickname: token.nickname as string,
        email: token.email as string,
        avatarUrl: token.avatarUrl as string,
        isActive: token.isActive as boolean,
        role: token.role as string,
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // 登录后重定向到仪表板
      if (url.startsWith('/')) return `${baseUrl}${url}`
      if (new URL(url).origin === baseUrl) return url
      return `${baseUrl}/dashboard`
    },
  },
  pages: {
    signIn: '/login',
    error: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === 'development',
}
