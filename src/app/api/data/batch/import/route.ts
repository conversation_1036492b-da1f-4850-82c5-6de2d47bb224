import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as XLSX from 'xlsx'

// POST /api/data/batch/import - 批量导入数据
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const formId = formData.get('formId') as string

    if (!file || !formId) {
      return NextResponse.json(
        { success: false, error: '缺少文件或表单ID' },
        { status: 400 }
      )
    }

    // 检查文件格式
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: '文件格式不支持，请上传Excel或CSV文件' },
        { status: 400 }
      )
    }

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isActive: true },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已禁用' },
        { status: 404 }
      )
    }

    const tableName = formConfig.tableName
    const fieldMapping = formConfig.fieldMapping as Record<string, any>

    // 读取文件内容
    const buffer = await file.arrayBuffer()
    let worksheetData: any[] = []

    if (file.type === 'text/csv') {
      // 处理CSV文件
      const csvText = new TextDecoder('utf-8').decode(buffer)
      const workbook = XLSX.read(csvText, { type: 'string' })
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      worksheetData = XLSX.utils.sheet_to_json(worksheet)
    } else {
      // 处理Excel文件
      const workbook = XLSX.read(buffer, { type: 'buffer' })
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      worksheetData = XLSX.utils.sheet_to_json(worksheet)
    }

    if (worksheetData.length === 0) {
      return NextResponse.json(
        { success: false, error: '文件中没有数据' },
        { status: 400 }
      )
    }

    // 验证数据格式
    const firstRow = worksheetData[0]
    const requiredFields = ['序号']
    const fieldNames = Object.values(fieldMapping).map(
      (config: any) => config.name
    )

    const missingFields = requiredFields.filter(field => !(field in firstRow))
    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `缺少必要字段: ${missingFields.join(', ')}`,
        },
        { status: 400 }
      )
    }

    // 处理数据导入
    const results = {
      total: worksheetData.length,
      success: 0,
      failed: 0,
      errors: [] as Array<{ row: number; error: string; data: any }>,
    }

    for (let i = 0; i < worksheetData.length; i++) {
      const rowData = worksheetData[i]
      const rowNumber = i + 2 // Excel行号从2开始（排除标题行）

      try {
        // 构建插入数据
        const insertData: any = {
          serial_number: rowData['序号'] || null,
          source_ip:
            request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'batch_import',
          creator_name: session.user.username || session.user.email,
          raw_data: JSON.stringify(rowData),
        }

        // 映射字段
        Object.entries(fieldMapping).forEach(([dbField, config]) => {
          const fieldName = (config as any).name
          if (fieldName in rowData) {
            let value = rowData[fieldName]

            // 数据类型转换
            if (
              (config as any).type === 'number' &&
              value !== null &&
              value !== ''
            ) {
              value = parseFloat(value)
              if (isNaN(value)) value = null
            } else if ((config as any).type === 'date' && value) {
              // 处理日期格式
              const date = new Date(value)
              if (!isNaN(date.getTime())) {
                value = date.toISOString().split('T')[0]
              } else {
                value = null
              }
            }

            insertData[dbField] = value
          }
        })

        // 检查必填字段
        const missingRequired = Object.entries(fieldMapping)
          .filter(([_, config]) => (config as any).required && !insertData[_])
          .map(([_, config]) => (config as any).name)

        if (missingRequired.length > 0) {
          results.errors.push({
            row: rowNumber,
            error: `缺少必填字段: ${missingRequired.join(', ')}`,
            data: rowData,
          })
          results.failed++
          continue
        }

        // 构建插入SQL
        const fields = Object.keys(insertData)
        const placeholders = fields.map(() => '?').join(',')
        const insertQuery = `
          INSERT INTO \`${tableName}\` (\`${fields.join('`, `')}\`, created_at, updated_at)
          VALUES (${placeholders}, NOW(), NOW())
        `

        await prisma.$executeRawUnsafe(
          insertQuery,
          ...Object.values(insertData)
        )
        results.success++
      } catch (error) {
        console.error(`导入第${rowNumber}行数据失败:`, error)
        results.errors.push({
          row: rowNumber,
          error: error instanceof Error ? error.message : '未知错误',
          data: rowData,
        })
        results.failed++
      }
    }

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: 'BATCH_IMPORT_DATA',
        resource: 'FormData',
        resourceId: formId,
        details: {
          formId,
          tableName,
          fileName: file.name,
          fileSize: file.size,
          totalRecords: results.total,
          successRecords: results.success,
          failedRecords: results.failed,
          errorSample: results.errors.slice(0, 5), // 只记录前5个错误
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: `导入完成，成功 ${results.success} 条，失败 ${results.failed} 条`,
      data: results,
    })
  } catch (error) {
    console.error('批量导入失败:', error)
    return NextResponse.json(
      { success: false, error: '批量导入失败' },
      { status: 500 }
    )
  }
}
