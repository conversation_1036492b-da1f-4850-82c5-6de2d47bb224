import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { cache } from '@/lib/cache'

// GET /api/health - 健康检查端点
export async function GET() {
  try {
    const startTime = Date.now()

    // 检查数据库连接
    let dbStatus = 'unknown'
    let dbLatency = 0
    try {
      const dbStart = Date.now()
      await prisma.$queryRaw`SELECT 1`
      dbLatency = Date.now() - dbStart
      dbStatus = 'healthy'
    } catch (error) {
      dbStatus = 'unhealthy'
      console.error('数据库健康检查失败:', error)
    }

    // 检查缓存状态
    const cacheStats = cache.getStats()

    // 检查磁盘空间（简单检查）
    let diskStatus = 'unknown'
    try {
      // 在Node.js中检查磁盘空间
      const fs = require('fs')
      const stats = fs.statSync('.')
      diskStatus = 'healthy'
    } catch (error) {
      diskStatus = 'unhealthy'
    }

    const totalLatency = Date.now() - startTime

    // 确定整体状态
    const isHealthy =
      dbStatus === 'healthy' && diskStatus === 'healthy' && totalLatency < 1000

    const healthData = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: {
          status: dbStatus,
          latency: `${dbLatency}ms`,
        },
        cache: {
          status: 'healthy',
          size: cacheStats.size,
          keys: cacheStats.keys.length,
        },
        disk: {
          status: diskStatus,
        },
        memory: {
          status: 'healthy',
          usage: {
            rss: `${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB`,
            heapTotal: `${Math.round(process.memoryUsage().heapTotal / 1024 / 1024)}MB`,
            heapUsed: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
            external: `${Math.round(process.memoryUsage().external / 1024 / 1024)}MB`,
          },
        },
      },
      latency: `${totalLatency}ms`,
    }

    const statusCode = isHealthy ? 200 : 503

    return NextResponse.json(healthData, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    })
  } catch (error) {
    console.error('健康检查失败:', error)

    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      {
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      }
    )
  }
}
