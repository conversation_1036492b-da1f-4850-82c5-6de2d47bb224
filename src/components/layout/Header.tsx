'use client'

import {
  Layout,
  Breadcrumb,
  Space,
  Avatar,
  Dropdown,
  Typography,
  Badge,
} from 'antd'
import {
  UserOutlined,
  LogoutOutlined,
  KeyOutlined,
  BellOutlined,
  SettingOutlined,
} from '@ant-design/icons'
import { usePathname } from 'next/navigation'
import Link from 'next/link'

const { Header: AntHeader } = Layout
const { Text } = Typography

interface HeaderProps {
  user?: {
    id: number
    username: string
    nickname?: string
    avatarUrl?: string
  }
  onLogout?: () => void
}

// 路径映射配置
const pathNameMap: Record<string, string> = {
  '/dashboard': '仪表板',
  '/forms': '表单管理',
  '/forms/config': '表单配置',
  '/forms/list': '表单列表',
  '/data': '数据管理',
  '/data/view': '数据查看',
  '/data/export': '数据导出',
  '/data/batch': '批量操作',
  '/settings': '系统设置',
  '/settings/profile': '个人设置',
  '/settings/password': '修改密码',
  '/settings/users': '用户管理',
  '/settings/logs': '系统日志',
  '/help': '帮助文档',
}

export function Header({ user, onLogout }: HeaderProps) {
  const pathname = usePathname()

  // 生成面包屑
  const generateBreadcrumb = (path: string) => {
    const pathSegments = path.split('/').filter(Boolean)
    const breadcrumbItems = [
      {
        title: (
          <Link
            href="/dashboard"
            className="text-gray-600 hover:text-primary-500"
          >
            首页
          </Link>
        ),
        key: 'dashboard',
      },
    ]

    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      if (pathNameMap[currentPath]) {
        const isLast = index === pathSegments.length - 1
        breadcrumbItems.push({
          title: isLast ? (
            <Text className="text-gray-900 font-medium">
              {pathNameMap[currentPath]}
            </Text>
          ) : (
            <Link
              href={currentPath}
              className="text-gray-600 hover:text-primary-500"
            >
              {pathNameMap[currentPath]}
            </Link>
          ),
          key: currentPath,
        })
      }
    })

    return breadcrumbItems
  }

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: (
        <Link href="/settings/profile" className="flex items-center">
          个人设置
        </Link>
      ),
    },
    {
      key: 'password',
      icon: <KeyOutlined />,
      label: (
        <Link href="/settings/password" className="flex items-center">
          修改密码
        </Link>
      ),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: (
        <Link href="/settings" className="flex items-center">
          系统设置
        </Link>
      ),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: onLogout,
      className: 'text-red-500 hover:text-red-600',
    },
  ]

  return (
    <AntHeader className="flex items-center justify-between px-6 bg-white shadow-ant border-b border-gray-200">
      {/* 左侧面包屑 */}
      <div className="flex-1">
        <Breadcrumb items={generateBreadcrumb(pathname)} />
      </div>

      {/* 右侧用户信息和操作 */}
      <div className="flex items-center">
        <Space size="middle">
          {/* 通知铃铛 */}
          <Badge count={3} size="small" offset={[-2, 2]}>
            <div className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">
              <BellOutlined className="text-gray-600 text-lg" />
            </div>
          </Badge>

          {/* 用户信息下拉菜单 */}
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
            arrow={{ pointAtCenter: true }}
          >
            <div className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <Avatar
                size="small"
                src={user?.avatarUrl}
                icon={!user?.avatarUrl && <UserOutlined />}
                className="border border-gray-200"
              />
              <div className="hidden sm:flex flex-col items-start">
                <Text className="text-sm font-medium text-gray-900 leading-none">
                  {user?.nickname || user?.username || '用户'}
                </Text>
                <Text className="text-xs text-gray-500 leading-none mt-0.5">
                  {user?.username}
                </Text>
              </div>
            </div>
          </Dropdown>
        </Space>
      </div>
    </AntHeader>
  )
}
