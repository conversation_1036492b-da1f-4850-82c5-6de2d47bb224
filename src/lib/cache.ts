// 简单的内存缓存实现
class MemoryCache {
  private cache: Map<string, { value: any; expiry: number }> = new Map()
  private timers: Map<string, NodeJS.Timeout> = new Map()

  set(key: string, value: any, ttlSeconds: number = 300): void {
    // 清除现有的定时器
    const existingTimer = this.timers.get(key)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 设置过期时间
    const expiry = Date.now() + ttlSeconds * 1000
    this.cache.set(key, { value, expiry })

    // 设置自动清理定时器
    const timer = setTimeout(() => {
      this.delete(key)
    }, ttlSeconds * 1000)

    this.timers.set(key, timer)
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) {
      return null
    }

    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.delete(key)
      return null
    }

    return item.value as T
  }

  delete(key: string): boolean {
    const timer = this.timers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(key)
    }
    return this.cache.delete(key)
  }

  clear(): void {
    // 清除所有定时器
    for (const timer of Array.from(this.timers.values())) {
      clearTimeout(timer)
    }
    this.timers.clear()
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }

  // 获取缓存统计信息
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    }
  }
}

// 创建全局缓存实例
export const cache = new MemoryCache()

// 缓存装饰器函数
export function cacheResult<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator: (...args: Parameters<T>) => string,
  ttlSeconds: number = 300
): T {
  return (async (...args: Parameters<T>) => {
    const cacheKey = keyGenerator(...args)

    // 尝试从缓存获取
    const cachedResult = cache.get(cacheKey)
    if (cachedResult !== null) {
      return cachedResult
    }

    // 执行原函数
    const result = await fn(...args)

    // 存储到缓存
    cache.set(cacheKey, result, ttlSeconds)

    return result
  }) as T
}

// 预定义的缓存键生成器
export const cacheKeys = {
  userProfile: (userId: string) => `user:profile:${userId}`,
  formConfig: (formId: string) => `form:config:${formId}`,
  formList: (page: number, limit: number, search: string) =>
    `forms:list:${page}:${limit}:${search}`,
  dashboardStats: () => 'dashboard:stats',
  userList: (page: number, limit: number, search: string) =>
    `users:list:${page}:${limit}:${search}`,
  systemLogs: (page: number, limit: number, filters: string) =>
    `logs:list:${page}:${limit}:${filters}`,
}

// 缓存失效函数
export const invalidateCache = {
  user: (userId?: string) => {
    if (userId) {
      cache.delete(cacheKeys.userProfile(userId))
    }
    // 清除用户列表缓存
    const allKeys = cache.getStats().keys
    allKeys.forEach(key => {
      if (key.startsWith('users:list:')) {
        cache.delete(key)
      }
    })
  },

  form: (formId?: string) => {
    if (formId) {
      cache.delete(cacheKeys.formConfig(formId))
    }
    // 清除表单列表缓存
    const allKeys = cache.getStats().keys
    allKeys.forEach(key => {
      if (key.startsWith('forms:list:')) {
        cache.delete(key)
      }
    })
    // 清除仪表板缓存
    cache.delete(cacheKeys.dashboardStats())
  },

  dashboard: () => {
    cache.delete(cacheKeys.dashboardStats())
  },

  logs: () => {
    const allKeys = cache.getStats().keys
    allKeys.forEach(key => {
      if (key.startsWith('logs:list:')) {
        cache.delete(key)
      }
    })
  },

  all: () => {
    cache.clear()
  },
}

// 缓存中间件函数
export async function withCache<T>(
  cacheKey: string,
  fn: () => Promise<T>,
  ttlSeconds: number = 300
): Promise<T> {
  // 尝试从缓存获取
  const cachedResult = cache.get<T>(cacheKey)
  if (cachedResult !== null) {
    return cachedResult
  }

  // 执行函数并缓存结果
  const result = await fn()
  cache.set(cacheKey, result, ttlSeconds)

  return result
}
