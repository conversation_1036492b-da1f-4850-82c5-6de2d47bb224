/**
 * PM2 配置文件 - 肺功能数据管理平台 (简化版)
 *
 * 参照 Dockerfile.optimized 和 Next.js standalone 模式进行优化。
 *
 * 使用方法：
 * 1. 确保 next.config.js 中有 output: 'standalone'
 * 2. 构建应用: yarn build
 * 3. 启动服务: pm2 start ecosystem.config.js
 */

module.exports = {
  apps: [
    {
      // ==================== 基础配置 ====================
      name: 'lung-function-admin',
      // 关键：直接运行 Next.js standalone 模式的输出结果
      // 使用npm run start
      script: 'npm',
      args: 'run start',
      cwd: process.cwd(),

      // ==================== 进程管理配置 ====================
      instances: 1, // 'max' for cluster mode
      exec_mode: 'fork', // 'cluster' for multi-core usage
      autorestart: true,
      watch: false, // 生产环境建议关闭
      max_memory_restart: '1G',

      // ==================== 性能与环境 ====================
      // 传递给 Node.js 的参数
      node_args: '--max-old-space-size=1024',

      // 生产环境变量
      env_production: {
        NODE_ENV: 'production',
        PORT: 3011, // 确保这个端口没有被占用
      },

      // ==================== 日志配置 ====================
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      merge_logs: true,
    },
  ],
};
