# 问题排查指南

本文档提供了肺功能数据管理平台常见问题的排查和解决方案。

## 📋 目录

- [应用启动问题](#应用启动问题)
- [数据库连接问题](#数据库连接问题)
- [认证和授权问题](#认证和授权问题)
- [Webhook接收问题](#webhook接收问题)
- [文件上传问题](#文件上传问题)
- [性能问题](#性能问题)
- [部署问题](#部署问题)
- [网络和代理问题](#网络和代理问题)
- [日志分析](#日志分析)
- [监控和诊断工具](#监控和诊断工具)

## 🚀 应用启动问题

### 问题：应用无法启动

**症状**：
- 运行 `npm run dev` 或 `pm2 start` 后应用无法启动
- 端口占用错误
- 依赖缺失错误

**排查步骤**：

1. **检查端口占用**
```bash
# 检查3011端口是否被占用
sudo netstat -tlnp | grep :3011
# 或使用lsof
sudo lsof -i :3011

# 如果端口被占用，杀死进程
sudo kill -9 <PID>
```

2. **检查Node.js版本**
```bash
node --version  # 应该 >= 18.0.0
npm --version
```

3. **重新安装依赖**
```bash
rm -rf node_modules package-lock.json
npm install
```

4. **检查环境变量**
```bash
# 确保.env.local或.env.production存在
ls -la .env*
# 检查必需的环境变量
cat .env.local | grep -E "(DATABASE_URL|NEXTAUTH_SECRET|NEXTAUTH_URL)"
```

5. **查看详细错误日志**
```bash
# 开发环境
npm run dev

# 生产环境
pm2 logs
pm2 show <app-name>
```

### 问题：构建失败

**症状**：
- `npm run build` 失败
- TypeScript编译错误
- 依赖版本冲突

**解决方案**：

1. **清理缓存**
```bash
npm run clean
rm -rf .next
npm run build
```

2. **检查TypeScript错误**
```bash
npm run type-check
```

3. **更新依赖**
```bash
npm update
npm audit fix
```

## 🗄️ 数据库连接问题

### 问题：数据库连接失败

**症状**：
- "Can't connect to MySQL server"
- "Access denied for user"
- "Unknown database"

**排查步骤**：

1. **测试数据库连接**
```bash
# 使用mysql客户端测试
mysql -h <host> -P <port> -u <username> -p <database>

# 或使用telnet测试端口
telnet <host> <port>
```

2. **检查环境变量格式**
```bash
# 正确的格式
DATABASE_URL="mysql://username:password@host:port/database"

# 腾讯云示例
DATABASE_URL="mysql://srmyy_123:<EMAIL>:23387/srmyy_123"
```

3. **检查数据库权限**
```sql
-- 在MySQL中执行
SHOW GRANTS FOR 'username'@'%';
SELECT User, Host FROM mysql.user WHERE User = 'username';
```

4. **检查防火墙和安全组**
```bash
# 检查本地防火墙
sudo ufw status
sudo iptables -L

# 腾讯云：检查安全组规则
# 确保MySQL端口(3306或自定义端口)已开放
```

### 问题：数据库迁移失败

**症状**：
- `npx prisma db push` 失败
- 表结构不匹配
- 数据丢失

**解决方案**：

1. **检查Prisma配置**
```bash
# 生成Prisma客户端
npx prisma generate

# 查看数据库状态
npx prisma db pull
npx prisma db push --preview-feature
```

2. **备份数据库**
```bash
mysqldump -h <host> -P <port> -u <username> -p <database> > backup.sql
```

3. **重置数据库**
```bash
# 谨慎操作：这会删除所有数据
npx prisma db push --force-reset
```

## 🔐 认证和授权问题

### 问题：无法登录

**症状**：
- 登录页面无响应
- "Invalid credentials" 错误
- 会话过期

**排查步骤**：

1. **检查NextAuth配置**
```bash
# 确保环境变量正确
echo $NEXTAUTH_URL
echo $NEXTAUTH_SECRET
```

2. **检查用户数据**
```sql
-- 在数据库中查看用户
SELECT * FROM User WHERE username = 'admin';
```

3. **重置管理员密码**
```bash
cd /path/to/project
node scripts/reset-password.js
```

4. **清除浏览器缓存**
- 清除cookies和localStorage
- 使用无痕模式测试

### 问题：会话管理问题

**症状**：
- 频繁要求重新登录
- 会话状态不一致

**解决方案**：

1. **检查JWT配置**
```javascript
// 在next-auth配置中
session: {
  strategy: "jwt",
  maxAge: 30 * 24 * 60 * 60, // 30 days
}
```

2. **检查cookie设置**
```javascript
cookies: {
  sessionToken: {
    name: `__Secure-next-auth.session-token`,
    options: {
      httpOnly: true,
      sameSite: 'lax',
      path: '/',
      secure: process.env.NODE_ENV === 'production'
    }
  }
}
```

## 🔄 Webhook接收问题

### 问题：Webhook数据接收失败

**症状**：
- 金数据推送失败
- 数据未保存到数据库
- 接收端点返回错误

**排查步骤**：

1. **检查Webhook端点**
```bash
# 测试端点可访问性
curl -X POST https://yourdomain.com/api/webhook/your-form-id \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

2. **查看Webhook日志**
```bash
# PM2日志
pm2 logs | grep webhook

# 或查看应用日志
tail -f /var/log/lung-function-admin.log | grep webhook
```

3. **验证表单配置**
```bash
# 检查表单配置是否存在
curl https://yourdomain.com/api/forms/your-form-id
```

4. **检查数据格式**
- 确保金数据推送的数据格式与配置匹配
- 验证字段映射是否正确

### 问题：动态表创建失败

**症状**：
- 新表单配置后无法创建数据表
- 字段类型不匹配

**解决方案**：

1. **检查数据库权限**
```sql
-- 确保用户有CREATE TABLE权限
SHOW GRANTS FOR 'username'@'%';
```

2. **验证字段配置**
```javascript
// 检查字段配置格式
{
  "fields": [
    {
      "name": "field_name",
      "type": "VARCHAR(255)",
      "required": true
    }
  ]
}
```

## 📁 文件上传问题

### 问题：文件上传失败

**症状**：
- 上传超时
- 文件大小限制错误
- 权限拒绝

**排查步骤**：

1. **检查文件大小限制**
```javascript
// 在next.config.js中
module.exports = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
}
```

2. **检查上传目录权限**
```bash
# 确保上传目录存在且有写权限
ls -la uploads/
chmod 755 uploads/
chown www-data:www-data uploads/
```

3. **检查磁盘空间**
```bash
df -h
```

## ⚡ 性能问题

### 问题：应用响应缓慢

**症状**：
- 页面加载时间长
- API响应慢
- 数据库查询超时

**排查步骤**：

1. **检查系统资源**
```bash
# CPU和内存使用情况
top
htop
free -h

# 磁盘I/O
iostat -x 1
```

2. **分析数据库性能**
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看进程列表
SHOW PROCESSLIST;

-- 分析查询
EXPLAIN SELECT * FROM your_table WHERE condition;
```

3. **优化数据库查询**
```sql
-- 添加索引
CREATE INDEX idx_created_at ON your_table(created_at);
CREATE INDEX idx_user_id ON your_table(user_id);
```

4. **检查应用缓存**
```bash
# 清理Next.js缓存
rm -rf .next
npm run build

# 重启PM2应用
pm2 reload all
```

### 问题：内存泄漏

**症状**：
- 内存使用持续增长
- 应用崩溃
- OOM错误

**解决方案**：

1. **监控内存使用**
```bash
# PM2内存监控
pm2 monit

# 设置内存限制
pm2 start app.js --max-memory-restart 1G
```

2. **分析内存使用**
```bash
# Node.js内存分析
node --inspect app.js
# 然后在Chrome中打开 chrome://inspect
```

## 🐳 部署问题

### 问题：Docker容器启动失败

**症状**：
- 容器无法启动
- 镜像构建失败
- 容器健康检查失败

**排查步骤**：

1. **查看容器日志**
```bash
docker-compose logs app
docker logs <container-id>
```

2. **检查容器状态**
```bash
docker-compose ps
docker inspect <container-id>
```

3. **重新构建镜像**
```bash
docker-compose build --no-cache
docker-compose up -d
```

4. **检查资源限制**
```bash
# 检查Docker资源使用
docker stats
```

### 问题：Nginx配置问题

**症状**：
- 502 Bad Gateway
- 404 Not Found
- SSL证书错误

**解决方案**：

1. **测试Nginx配置**
```bash
sudo nginx -t
sudo nginx -s reload
```

2. **检查上游服务**
```bash
# 测试应用是否在运行
curl http://localhost:3011/api/health
```

3. **查看Nginx日志**
```bash
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

## 🌐 网络和代理问题

### 问题：CORS错误

**症状**：
- 跨域请求被阻止
- 前端无法访问API

**解决方案**：

1. **配置CORS**
```javascript
// 在API路由中
export async function POST(request: Request) {
  const response = new Response(JSON.stringify(data), {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
  return response;
}
```

### 问题：SSL/HTTPS问题

**症状**：
- 证书过期
- 混合内容错误
- 不安全连接警告

**解决方案**：

1. **检查证书状态**
```bash
# 检查证书有效期
sudo certbot certificates

# 续期证书
sudo certbot renew
```

2. **测试SSL配置**
```bash
openssl s_client -connect yourdomain.com:443
```

## 📊 日志分析

### 应用日志位置

```bash
# PM2日志
~/.pm2/logs/

# 系统日志
/var/log/lung-function-admin.log

# Nginx日志
/var/log/nginx/access.log
/var/log/nginx/error.log

# MySQL日志
/var/log/mysql/error.log
/var/log/mysql/slow.log
```

### 日志分析命令

```bash
# 实时查看日志
tail -f /var/log/lung-function-admin.log

# 搜索错误
grep -i error /var/log/lung-function-admin.log

# 统计访问量
awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr

# 查看最近的错误
journalctl -u nginx -f
```

## 🔧 监控和诊断工具

### 系统监控

```bash
# 系统资源监控
htop
iotop
nethogs

# 进程监控
ps aux | grep node
pstree -p

# 网络监控
netstat -tlnp
ss -tlnp
```

### 应用监控

```bash
# PM2监控
pm2 monit
pm2 status
pm2 info <app-name>

# 数据库监控
mysqladmin -u root -p processlist
mysqladmin -u root -p status
```

### 健康检查

```bash
# 应用健康检查
curl http://localhost:3011/api/health

# 数据库健康检查
mysqladmin -u username -p ping

# 服务状态检查
systemctl status nginx
systemctl status mysql
```

## 🆘 紧急恢复

### 应用崩溃恢复

```bash
# 重启应用
pm2 restart all

# 如果PM2无响应
pkill -f node
pm2 kill
pm2 start ecosystem.config.js
```

### 数据库恢复

```bash
# 从备份恢复
mysql -u username -p database_name < backup.sql

# 修复表
mysqlcheck -u username -p --auto-repair database_name
```

### 回滚部署

```bash
# Git回滚
git reset --hard HEAD~1
npm install
npm run build
pm2 reload all
```

## 📞 获取帮助

如果问题仍未解决：

1. 📖 查看[部署指南](DEPLOYMENT.md)
2. 📝 查看[开发指南](DEVELOPMENT.md)
3. 🐛 提交[Issue](../../issues)
4. 💬 参与[讨论](../../discussions)
5. 📧 联系技术支持

---

**记住**：在进行任何重要操作前，请务必备份数据！
