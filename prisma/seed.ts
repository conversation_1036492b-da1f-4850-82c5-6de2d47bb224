import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('开始数据库种子数据创建...')

  // 创建默认管理员用户
  const hashedPassword = await bcrypt.hash('admin123456', 12)

  const adminUser = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      role: 'admin',
      doctor_name: '系统管理员',
      is_active: true,
      created_at: new Date(),
    },
  })

  console.log('创建管理员用户:', adminUser)

  // 创建测试用户
  const testHashedPassword = await bcrypt.hash('test123456', 12)

  const testUser = await prisma.user.upsert({
    where: { username: 'test' },
    update: {},
    create: {
      username: 'test',
      email: '<EMAIL>',
      password_hash: testHashedPassword,
      role: 'user',
      doctor_name: '测试用户',
      is_active: true,
      created_at: new Date(),
    },
  })

  console.log('创建测试用户:', testUser)

  // 创建示例表单配置
  const sampleFormConfig = await prisma.formConfig.upsert({
    where: { formId: 'ZFs2eo' },
    update: {},
    create: {
      formId: 'ZFs2eo',
      formName: '预约免费肺功能检查、免费办理慢性病医保',
      fieldMapping: {
        field_1: { name: '姓名', type: 'string' },
        field_2: { name: '选择项', type: 'string' },
        field_3: { name: '电话', type: 'string' },
        field_4: { name: '多选项', type: 'array' },
        field_6: { name: '数字', type: 'number' },
        field_7: { name: '其他选项', type: 'string' },
        x_field_1: { name: '文本内容', type: 'text' },
      },
      tableName: 'form_data_ZFs2eo',
      tableCreated: false,
      webhookUrl: '/api/webhook/ZFs2eo',
      isActive: true,
      createdById: adminUser.id,
    },
  })

  console.log('创建示例表单配置:', sampleFormConfig)

  // 创建系统日志示例
  await prisma.systemLog.create({
    data: {
      action: 'system_init',
      resource: 'system',
      resourceId: null,
      details: {
        level: 'info',
        message: '系统初始化完成',
        version: '1.0.0',
      },
      userId: adminUser.id,
      ipAddress: '127.0.0.1',
      userAgent: 'System',
    },
  })

  console.log('数据库种子数据创建完成！')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async e => {
    console.error('种子数据创建失败:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
