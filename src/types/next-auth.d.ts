import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: number
      username: string
      nickname?: string
      email?: string
      avatarUrl?: string
      isActive: boolean
      role: string
    }
  }

  interface User {
    id: number
    username: string
    nickname?: string
    email?: string
    avatarUrl?: string
    isActive: boolean
    role: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: number
    username: string
    nickname?: string
    email?: string
    avatarUrl?: string
    isActive: boolean
    role: string
  }
}
