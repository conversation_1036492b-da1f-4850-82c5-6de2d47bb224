# 变更日志

本文档记录了肺功能数据管理平台的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 文档整理和优化
- 统一的部署指南
- 贡献指南和开发规范

### 变更
- 重新组织项目文档结构
- 合并重复的README文档
- 优化文档格式和内容

### 修复
- 修复文档中的链接错误
- 统一文档命名规范

## [1.0.0] - 2024-01-15

### 新增
- 🎉 项目初始发布
- 👤 用户认证和授权系统
- 📝 表单配置管理功能
- 🔄 Webhook数据接收处理
- 🗄️ 动态数据表创建和管理
- 📊 数据查询、编辑和批量操作
- 📤 数据导出功能 (Excel/CSV)
- 🔍 高级搜索和筛选
- 📱 响应式用户界面设计
- 🐳 Docker容器化部署支持
- 🔒 完整的安全防护措施
- 📋 系统操作日志和审计
- ⚡ 性能优化和缓存机制

### 技术特性
- Next.js 14 (App Router) 框架
- TypeScript 严格模式
- Ant Design 5.x UI组件库
- Tailwind CSS 样式系统
- Prisma ORM 数据库管理
- NextAuth.js 认证系统
- MySQL 8.0 数据库支持
- PM2 进程管理
- Nginx 反向代理配置

### 安全特性
- bcrypt 密码加密
- JWT 会话管理
- CSRF 攻击防护
- XSS 攻击防护
- SQL 注入防护
- 文件上传安全检查
- 操作权限控制

### 部署支持
- Docker Compose 一键部署
- 传统部署脚本
- 腾讯云部署指南
- SSL/HTTPS 配置
- 自动化部署脚本
- 健康检查端点

## [0.9.0] - 2023-12-20

### 新增
- 🧪 测试版本发布
- 基础的用户管理功能
- 简单的表单配置
- 基本的数据接收功能

### 变更
- 项目架构设计
- 数据库模式设计
- API接口设计

## [0.8.0] - 2023-12-01

### 新增
- 🏗️ 项目架构搭建
- 基础的Next.js应用框架
- 数据库连接配置
- 基本的认证系统

### 技术债务
- 需要完善错误处理
- 需要添加测试用例
- 需要优化性能

## [0.7.0] - 2023-11-15

### 新增
- 📋 项目需求分析
- 🎨 UI/UX设计
- 🏗️ 技术栈选择
- 📝 项目文档初稿

### 计划
- 用户认证系统开发
- 表单配置功能开发
- 数据管理功能开发

## 版本说明

### 版本号格式
本项目使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 变更类型

- **新增** (Added)：新功能
- **变更** (Changed)：对现有功能的变更
- **弃用** (Deprecated)：即将移除的功能
- **移除** (Removed)：已移除的功能
- **修复** (Fixed)：任何bug修复
- **安全** (Security)：安全相关的修复

### 发布周期

- **主版本**：重大功能更新或架构变更
- **次版本**：新功能发布，每月一次
- **修订版本**：Bug修复，根据需要发布

### 支持政策

- **当前版本**：完全支持，包括新功能和安全更新
- **前一个主版本**：安全更新和关键Bug修复
- **更早版本**：不再支持，建议升级

### 升级指南

#### 从 0.x 升级到 1.0

1. **数据库迁移**
   ```bash
   npx prisma db push
   ```

2. **环境变量更新**
   - 添加 `NEXTAUTH_SECRET`
   - 更新 `DATABASE_URL` 格式

3. **配置文件更新**
   - 更新 `ecosystem.config.js`
   - 更新 Nginx 配置

4. **依赖更新**
   ```bash
   npm install
   ```

#### 破坏性变更

**1.0.0**
- API 路径变更：`/api/v1/` 前缀移除
- 数据库表结构变更
- 环境变量命名变更

### 已知问题

#### 当前版本 (1.0.0)
- 大文件上传可能超时
- 批量操作性能有待优化
- 移动端某些功能体验需改进

#### 计划修复
- 实现文件分片上传
- 优化数据库查询性能
- 改进移动端响应式设计

### 贡献者

感谢以下贡献者对项目的支持：

- [@peckbyte](https://github.com/peckbyte) - 项目创建者和主要维护者
- 以及所有提交Issue和PR的贡献者

### 反馈和建议

如果您有任何问题或建议：

1. 📖 查看[文档](docs/)
2. 🐛 提交[Issue](../../issues)
3. 💬 参与[讨论](../../discussions)
4. 📧 联系维护者

---

**注意**：此变更日志将持续更新，记录项目的所有重要变更。
