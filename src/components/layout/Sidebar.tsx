'use client'

import { Layout, Menu, Typography } from 'antd'
import {
  DashboardOutlined,
  FormOutlined,
  DatabaseOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  FileTextOutlined,
  TableOutlined,
  ExportOutlined,
  EditOutlined,
  UserOutlined,
  KeyOutlined,
  TeamOutlined,
  FileSearchOutlined,
  ExclamationCircleOutlined,
  BugOutlined,
} from '@ant-design/icons'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { useState, useEffect } from 'react'

const { Sider } = Layout
const { Text } = Typography

interface SidebarProps {
  collapsed?: boolean
  onCollapse?: (collapsed: boolean) => void
}

// 菜单项配置
const menuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: <Link href="/dashboard">仪表板</Link>,
  },
  {
    key: 'forms',
    icon: <FormOutlined />,
    label: '表单管理',
    children: [
      {
        key: '/forms/config',
        icon: <FileTextOutlined />,
        label: <Link href="/forms/config">表单配置</Link>,
      },
      {
        key: '/forms/list',
        icon: <TableOutlined />,
        label: <Link href="/forms/list">表单列表</Link>,
      },
    ],
  },
  {
    key: '/data',
    icon: <DatabaseOutlined />,
    label: '数据管理',
    children: [
      {
        key: '/data/view',
        icon: <FileSearchOutlined />,
        label: <Link href="/data/view">数据查看</Link>,
      },
      {
        key: '/data/export',
        icon: <ExportOutlined />,
        label: <Link href="/data/export">数据导出</Link>,
      },
      {
        key: '/data/batch',
        icon: <EditOutlined />,
        label: <Link href="/data/batch">批量操作</Link>,
      },
    ],
  },
  {
    key: 'monitoring',
    icon: <BugOutlined />,
    label: '监控诊断',
    children: [
      {
        key: '/validation-failures',
        icon: <ExclamationCircleOutlined />,
        label: <Link href="/validation-failures">验证失败记录</Link>,
      },
    ],
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '系统设置',
    children: [
      {
        key: '/settings/profile',
        icon: <UserOutlined />,
        label: <Link href="/settings/profile">个人设置</Link>,
      },
      {
        key: '/settings/password',
        icon: <KeyOutlined />,
        label: <Link href="/settings/password">修改密码</Link>,
      },
      {
        key: '/settings/users',
        icon: <TeamOutlined />,
        label: <Link href="/settings/users">用户管理</Link>,
      },
      {
        key: '/settings/logs',
        icon: <FileTextOutlined />,
        label: <Link href="/settings/logs">系统日志</Link>,
      },
    ],
  },
  {
    key: '/help',
    icon: <QuestionCircleOutlined />,
    label: <Link href="/help">帮助文档</Link>,
  },
]

export function Sidebar({
  collapsed: externalCollapsed,
  onCollapse,
}: SidebarProps) {
  const [internalCollapsed, setInternalCollapsed] = useState(false)
  const pathname = usePathname()

  // 如果外部传入了 collapsed 状态，则使用外部状态
  const collapsed =
    externalCollapsed !== undefined ? externalCollapsed : internalCollapsed

  // 处理折叠状态变化
  const handleCollapse = (newCollapsed: boolean) => {
    if (onCollapse) {
      onCollapse(newCollapsed)
    } else {
      setInternalCollapsed(newCollapsed)
    }
  }

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    return [pathname]
  }

  // 获取默认展开的菜单项
  const getDefaultOpenKeys = () => {
    const openKeys: string[] = []

    if (pathname.startsWith('/forms')) {
      openKeys.push('forms')
    }
    if (pathname.startsWith('/data')) {
      openKeys.push('/data')
    }
    if (pathname.startsWith('/validation-failures')) {
      openKeys.push('monitoring')
    }
    if (pathname.startsWith('/settings')) {
      openKeys.push('/settings')
    }

    return openKeys
  }

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={handleCollapse}
      className="shadow-ant-hover"
      theme="dark"
      width={256}
      collapsedWidth={80}
      breakpoint="lg"
      onBreakpoint={broken => {
        // 在移动端自动折叠
        if (broken && !collapsed) {
          handleCollapse(true)
        }
      }}
    >
      {/* Logo 区域 */}
      <div className="h-16 flex items-center justify-center px-4 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
            <DatabaseOutlined className="text-white text-lg" />
          </div>
          {!collapsed && (
            <div className="flex flex-col">
              <Text className="text-white font-bold text-sm leading-none">
                肺功能数据
              </Text>
              <Text className="text-gray-300 text-xs leading-none mt-0.5">
                管理平台
              </Text>
            </div>
          )}
        </div>
      </div>

      {/* 菜单区域 */}
      <div className="flex-1 overflow-auto scrollbar-hide">
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getDefaultOpenKeys()}
          items={menuItems}
          className="border-r-0"
          inlineCollapsed={collapsed}
        />
      </div>

      {/* 底部信息 */}
      {!collapsed && (
        <div className="p-4 border-t border-gray-700">
          <div className="text-center">
            <Text className="text-gray-400 text-xs">版本 1.0.0</Text>
          </div>
        </div>
      )}
    </Sider>
  )
}
