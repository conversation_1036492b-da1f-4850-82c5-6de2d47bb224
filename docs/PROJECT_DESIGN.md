# 肺功能数据管理平台 - 产品设计文档

## 项目概述

### 项目名称

肺功能数据管理平台 (Lung Function Data Management Platform)

### 项目目标

构建一个现代化的数据管理平台，用于接收金数据平台的webhook数据，并提供完整的数据管理、分析和导出功能。

### 核心价值

- 自动化数据收集和处理
- 直观的数据管理界面
- 灵活的表单配置机制
- 安全的用户管理系统

## 技术架构

### 技术栈选择

- **前端框架**: Next.js 14 (App Router)
- **UI组件库**: Ant Design 5.x (蓝色主题)
- **样式方案**: Tailwind CSS + Ant Design
- **状态管理**: Zustand
- **数据库**: 腾讯云轻量数据库 (MySQL)
- **ORM**: Prisma
- **认证**: NextAuth.js + bcrypt
- **部署**: Docker + Docker Compose
- **开发环境**: macOS

### 项目结构

```
lung-function-admin/
├── README.md
├── PROJECT_DESIGN.md
├── DEVELOPMENT_PLAN.md
├── TECHNICAL_SPEC.md
├── CLAUDE.md
├── next.config.js
├── package.json
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/
│   │   │   └── login/          # 登录页面
│   │   ├── (dashboard)/
│   │   │   ├── page.tsx        # 仪表板首页
│   │   │   ├── forms/          # 表单管理
│   │   │   │   ├── config/     # 表单配置
│   │   │   │   └── list/       # 表单列表
│   │   │   ├── data/           # 数据管理
│   │   │   │   ├── view/[formId]/  # 数据查看
│   │   │   │   ├── export/     # 数据导出
│   │   │   │   └── batch/      # 批量操作
│   │   │   ├── settings/       # 系统设置
│   │   │   │   ├── profile/    # 个人设置
│   │   │   │   ├── password/   # 修改密码
│   │   │   │   ├── users/      # 用户管理
│   │   │   │   └── logs/       # 系统日志
│   │   │   ├── help/           # 帮助文档
│   │   │   └── layout.tsx      # 仪表板布局
│   │   ├── api/                # API Routes
│   │   │   ├── auth/           # 认证相关
│   │   │   ├── webhook/        # Webhook接收
│   │   │   ├── forms/          # 表单配置
│   │   │   ├── data/           # 数据操作
│   │   │   └── users/          # 用户管理
│   │   ├── globals.css
│   │   └── layout.tsx
│   ├── components/
│   │   ├── ui/                 # 基础UI组件
│   │   ├── forms/              # 表单相关组件
│   │   ├── data/               # 数据展示组件
│   │   ├── user/               # 用户相关组件
│   │   └── layout/             # 布局组件
│   ├── lib/
│   │   ├── db.ts               # 数据库连接
│   │   ├── auth.ts             # 认证配置
│   │   ├── logger.ts           # 日志工具
│   │   ├── utils.ts            # 工具函数
│   │   └── constants.ts        # 常量定义
│   ├── types/                  # TypeScript类型定义
│   └── store/                  # 状态管理
├── public/                     # 静态资源
├── logs/                       # 日志文件
├── docker-compose.yml
├── Dockerfile
└── .env.example
```

## 功能设计

### 1. 用户认证系统

#### 1.1 登录功能

- **登录方式**: 用户名 + 密码
- **安全特性**:
  - 密码bcrypt加密存储
  - 记住我功能 (30天有效期)
  - 登录失败次数限制
  - 会话自动续期
- **界面设计**:
  - 现代化登录界面
  - 蓝色主题风格
  - 响应式设计

#### 1.2 用户管理

- **个人设置**:
  - 修改昵称
  - 修改密码 (需验证原密码)
  - 修改邮箱
  - 上传头像
  - 查看登录历史
- **用户管理** (管理员功能):
  - 用户列表查看
  - 添加新用户
  - 禁用/启用用户
  - 重置用户密码

### 2. 表单配置管理

#### 2.1 表单配置流程

1. **JSON输入**: 管理员输入表单样例JSON
2. **字段解析**: 系统自动解析字段结构
3. **映射配置**: 配置字段映射关系
4. **表创建**: 自动生成对应数据表
5. **配置保存**: 保存配置到数据库

#### 2.2 动态表结构

- **表命名规则**: `form_data_{form_id}`
- **通用字段**: id, raw_data(JSON), created_at, updated_at
- **动态字段**: 根据JSON配置动态添加

#### 2.3 字段映射

- **基础映射**: field_1 -> 姓名, field_2 -> 电话等
- **类型转换**: 字符串、数字、日期、JSON数组
- **默认值**: 支持设置字段默认值

### 3. Webhook数据接收

#### 3.1 接收机制

- **端点格式**: `/api/webhook/[form_id]`
- **数据格式**: 金数据标准格式
- **处理流程**:
  1. 验证请求来源
  2. 解析JSON数据
  3. 查找表单配置
  4. 数据转换和存储
  5. 记录处理日志

#### 3.2 错误处理

- **重试机制**: 3次重试，指数退避
- **错误记录**: 详细的错误日志
- **数据备份**: 原始JSON完整保存
- **通知机制**: 处理失败时的通知

### 4. 数据管理界面

#### 4.1 数据展示

- **表格展示**: Ant Design Table组件
- **分页**: 支持大数据量分页显示
- **排序**: 多列排序支持
- **筛选**: 快速筛选和高级搜索

#### 4.2 CRUD操作

- **查看**: 数据详情查看
- **编辑**: 行内编辑和弹窗编辑
- **删除**: 单个删除和批量删除
- **添加**: 手动添加数据记录

#### 4.3 批量操作

- **批量选择**: 支持全选、反选
- **批量编辑**: 批量更新字段值
- **批量删除**: 批量删除记录
- **批量导出**: 批量导出选中数据

#### 4.4 高级搜索

- **多字段搜索**: 支持组合条件搜索
- **模糊匹配**: 文本字段模糊搜索
- **范围搜索**: 数字和日期范围搜索
- **状态记忆**: 搜索条件自动保存

### 5. 数据导出功能

#### 5.1 导出格式

- **Excel格式**: .xlsx文件导出
- **CSV格式**: .csv文件导出
- **自定义字段**: 选择导出字段
- **筛选导出**: 基于搜索条件导出

#### 5.2 导出配置

- **字段映射**: 自定义导出字段名称
- **数据格式化**: 日期、数字格式化
- **文件命名**: 自动生成文件名

### 6. 系统管理

#### 6.1 系统日志

- **操作日志**: 用户操作记录
- **错误日志**: 系统错误记录
- **访问日志**: 用户访问记录
- **日志查询**: 支持日志搜索和筛选

#### 6.2 系统设置

- **基础配置**: 系统基本信息设置
- **安全设置**: 密码策略、会话设置
- **通知设置**: 邮件、短信通知配置

## 界面设计

### 1. 设计风格

- **主题色**: 蓝色 (#1890ff)
- **设计语言**: Ant Design 设计规范
- **交互模式**: 现代化Web应用交互
- **响应式**: 支持桌面、平板、手机

### 2. 布局结构

- **顶部导航**: Logo、面包屑、用户菜单
- **侧边栏**: 主要功能导航菜单
- **主内容**: 页面主要内容区域
- **状态栏**: 操作状态和提示信息

### 3. 交互设计

- **表单验证**: 实时验证和错误提示
- **加载状态**: 数据加载时的友好提示
- **操作反馈**: 成功、失败、警告提醒
- **确认对话**: 敏感操作的二次确认

## 数据库设计

### 1. 用户相关表

#### users (用户表)

```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    nickname VARCHAR(100),
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    avatar_url VARCHAR(255),
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 表单配置表

#### form_configs (表单配置表)

```sql
CREATE TABLE form_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    form_id VARCHAR(50) UNIQUE NOT NULL,
    form_name VARCHAR(200) NOT NULL,
    field_mapping JSON NOT NULL,
    table_created BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. 动态数据表

每个表单类型对应一个独立的数据表，表名格式为 `form_data_{form_id}`

### 4. 系统日志表

#### system_logs (系统日志表)

```sql
CREATE TABLE system_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('info', 'warn', 'error') NOT NULL,
    message TEXT NOT NULL,
    context JSON,
    user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 安全性设计

### 1. 认证安全

- 密码复杂度要求
- 密码加密存储 (bcrypt)
- 会话管理和超时
- 登录失败限制

### 2. 数据安全

- SQL注入防护
- XSS攻击防护
- CSRF保护
- 数据验证和过滤

### 3. 访问控制

- 用户权限管理
- 路由保护
- API接口鉴权
- 操作日志记录

## 性能优化

### 1. 前端优化

- 代码分割和懒加载
- 静态资源优化
- 缓存策略
- 虚拟滚动 (大数据量表格)

### 2. 后端优化

- 数据库索引优化
- 查询性能优化
- API响应缓存
- 分页和限流

### 3. 部署优化

- Docker镜像优化
- 静态资源CDN
- 数据库连接池
- 负载均衡 (如需要)

## 部署方案

### 1. 开发环境

- **数据库连接**: 公网连接方式
- **端口**: 3000 (Next.js默认)
- **环境变量**: .env.local文件
- **开发工具**: VS Code + 相关插件

### 2. 生产环境

- **部署方式**: Docker + Docker Compose
- **数据库连接**: 内网连接方式
- **端口映射**: 3000:3000
- **日志管理**: 日志文件持久化
- **数据持久化**: 数据库和文件存储

### 3. 环境配置

```bash
# 开发环境
DATABASE_URL=mysql+pymysql://srmyy_123:<EMAIL>:23387/srmyy_123

# 生产环境
DATABASE_URL=mysql+pymysql://srmyy_123:gg9gpaEqkg4s@10.0.12.9:3306/srmyy_123
```

## 测试策略

### 1. 单元测试

- 工具函数测试
- API接口测试
- 组件单元测试

### 2. 集成测试

- 数据库操作测试
- Webhook接收测试
- 用户认证流程测试

### 3. 端到端测试

- 用户操作流程测试
- 数据管理完整流程测试
- 跨浏览器兼容性测试

## 维护和监控

### 1. 日志管理

- 应用日志记录
- 错误日志监控
- 性能日志分析

### 2. 监控指标

- 系统性能监控
- 数据库性能监控
- 用户行为分析

### 3. 备份策略

- 数据库定期备份
- 配置文件备份
- 日志文件归档

## 扩展性考虑

### 1. 功能扩展

- 数据可视化图表
- 报表生成功能
- 数据分析工具
- 移动端应用

### 2. 技术扩展

- 微服务架构
- 消息队列
- 缓存系统
- 搜索引擎

### 3. 集成扩展

- 第三方API集成
- 单点登录 (SSO)
- 企业微信/钉钉集成
- 邮件/短信通知
