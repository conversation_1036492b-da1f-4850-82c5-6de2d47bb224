const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function cleanupInvalidForms() {
  try {
    console.log('🔍 检查无效的表单配置...\n')

    // 获取所有表单配置
    const forms = await prisma.formConfig.findMany({
      select: {
        id: true,
        formId: true,
        formName: true,
        tableName: true,
        isActive: true
      }
    })

    console.log(`找到 ${forms.length} 个表单配置`)

    for (const form of forms) {
      console.log(`\n检查表单: ${form.formName} (${form.formId})`)
      console.log(`  表名: ${form.tableName}`)
      console.log(`  状态: ${form.isActive ? '活跃' : '禁用'}`)

      if (form.tableName) {
        // 检查表是否存在
        const tableExists = await checkTableExists(form.tableName)
        
        if (!tableExists) {
          console.log(`  ❌ 表 ${form.tableName} 不存在`)
          
          if (form.isActive) {
            console.log(`  🔧 禁用表单配置 ${form.formId}`)
            
            // 禁用这个表单配置而不是删除
            await prisma.formConfig.update({
              where: { id: form.id },
              data: { isActive: false }
            })
            
            console.log(`  ✅ 已禁用表单配置`)
          } else {
            console.log(`  ℹ️  表单配置已经是禁用状态`)
          }
        } else {
          console.log(`  ✅ 表存在`)
        }
      } else {
        console.log(`  ⚠️  表单配置没有指定表名`)
      }
    }

    console.log('\n🎉 清理完成!')

  } catch (error) {
    console.error('❌ 清理失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 检查表是否存在
async function checkTableExists(tableName) {
  try {
    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?`,
      tableName
    )
    return Number(result[0]?.count || 0) > 0
  } catch (error) {
    console.error(`检查表 ${tableName} 是否存在失败:`, error)
    return false
  }
}

cleanupInvalidForms()
