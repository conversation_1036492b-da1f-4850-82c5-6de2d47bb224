#!/bin/bash

# 修复CentOS 7上Node.js安装问题的脚本
# 解决glibc版本不兼容问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为CentOS 7
check_centos7() {
    if ! grep -q "CentOS Linux 7" /etc/os-release && ! grep -q "VERSION_ID=\"7\"" /etc/os-release; then
        log_error "此脚本仅适用于CentOS 7系统"
        exit 1
    fi
    log_info "确认为CentOS 7系统"
}

# 清理现有Node.js安装
cleanup_nodejs() {
    log_info "清理现有Node.js安装..."

    # 停止可能正在运行的Node.js应用进程（但不包括当前脚本）
    log_info "停止Node.js应用进程..."
    sudo pkill -f "node.*app" || true
    sudo pkill -f "npm.*start" || true
    sudo pkill -f "yarn.*start" || true
    sleep 2

    # 卸载现有的Node.js
    log_info "卸载现有Node.js包..."
    sudo yum remove -y nodejs npm || true

    # 清理NodeSource仓库
    log_info "清理NodeSource仓库配置..."
    sudo rm -f /etc/yum.repos.d/nodesource*.repo

    # 清理yum缓存
    log_info "清理yum缓存..."
    sudo yum clean all

    log_success "清理完成"
}

# 安装Node.js 16.x
install_nodejs16() {
    log_info "安装Node.js 16.x (兼容CentOS 7)..."
    
    # 添加Node.js 16.x仓库
    curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
    
    # 安装Node.js 16.x
    sudo yum install -y nodejs
    
    # 验证安装
    node_version=$(node -v)
    npm_version=$(npm -v)
    
    log_success "Node.js安装完成"
    log_info "Node.js版本: $node_version"
    log_info "npm版本: $npm_version"
}

# 安装全局包
install_global_packages() {
    log_info "安装全局包..."
    
    # 安装Yarn
    if ! command -v yarn &> /dev/null; then
        log_info "安装Yarn..."
        sudo npm install -g yarn
    fi
    
    # 安装PM2
    if ! command -v pm2 &> /dev/null; then
        log_info "安装PM2..."
        sudo npm install -g pm2
    fi
    
    log_success "全局包安装完成"
}

# 主函数
main() {
    log_info "开始修复CentOS 7上的Node.js安装问题..."
    
    check_centos7
    cleanup_nodejs
    install_nodejs16
    install_global_packages
    
    log_success "修复完成！"
    echo
    log_info "现在可以重新运行部署脚本："
    echo "  bash scripts/deploy-traditional.sh"
    echo
    log_warning "注意事项："
    echo "  - Node.js 16.x是最后一个支持CentOS 7的版本"
    echo "  - 建议考虑升级到CentOS 8+或使用Docker部署"
}

# 执行主函数
main "$@"
