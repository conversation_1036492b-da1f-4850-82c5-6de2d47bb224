#!/bin/bash

# 简单的Node.js修复脚本 - 适用于CentOS 7
# 直接安装Node.js 16.x而不进行复杂的清理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

main() {
    log_info "开始安装Node.js 16.x (兼容CentOS 7)..."
    
    # 检查系统
    if ! grep -q "CentOS Linux 7" /etc/os-release && ! grep -q "VERSION_ID=\"7\"" /etc/os-release; then
        log_error "此脚本仅适用于CentOS 7系统"
        exit 1
    fi
    
    # 清理可能存在的NodeSource仓库配置
    log_info "清理旧的NodeSource仓库配置..."
    sudo rm -f /etc/yum.repos.d/nodesource*.repo || true
    
    # 清理yum缓存
    sudo yum clean all
    
    # 添加Node.js 16.x仓库
    log_info "添加Node.js 16.x仓库..."
    curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
    
    # 安装Node.js 16.x（如果已存在会升级/降级到16.x）
    log_info "安装Node.js 16.x..."
    sudo yum install -y nodejs
    
    # 验证安装
    if command -v node &> /dev/null; then
        node_version=$(node -v)
        npm_version=$(npm -v)
        log_success "Node.js安装成功！"
        log_info "Node.js版本: $node_version"
        log_info "npm版本: $npm_version"
    else
        log_error "Node.js安装失败"
        exit 1
    fi
    
    # 安装全局包
    log_info "安装全局包..."
    
    if ! command -v yarn &> /dev/null; then
        log_info "安装Yarn..."
        sudo npm install -g yarn
    else
        log_info "Yarn已安装"
    fi
    
    if ! command -v pm2 &> /dev/null; then
        log_info "安装PM2..."
        sudo npm install -g pm2
    else
        log_info "PM2已安装"
    fi
    
    log_success "修复完成！"
    echo
    log_info "现在可以运行部署脚本："
    echo "  bash scripts/deploy-traditional.sh"
    echo
    log_info "或者手动进入项目目录安装依赖："
    echo "  cd ~/www/wwwroot/free_lung_function_project"
    echo "  yarn install"
}

main "$@"
