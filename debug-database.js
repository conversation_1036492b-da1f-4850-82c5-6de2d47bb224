const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function checkDatabase() {
  try {
    console.log('🔍 开始检查数据库状态...\n')

    // 1. 检查数据库连接
    console.log('1. 测试数据库连接...')
    await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ 数据库连接正常\n')

    // 2. 检查用户表
    console.log('2. 检查用户表...')
    const userCount = await prisma.user.count()
    console.log(`用户总数: ${userCount}`)
    
    if (userCount > 0) {
      const users = await prisma.user.findMany({
        take: 3,
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          is_active: true,
          created_at: true
        }
      })
      console.log('前3个用户:')
      users.forEach(user => {
        console.log(`  - ID: ${user.id}, 用户名: ${user.username}, 角色: ${user.role}, 活跃: ${user.is_active}`)
      })
    }
    console.log('')

    // 3. 检查表单配置
    console.log('3. 检查表单配置...')
    const formCount = await prisma.formConfig.count()
    console.log(`表单配置总数: ${formCount}`)
    
    if (formCount > 0) {
      const forms = await prisma.formConfig.findMany({
        take: 3,
        select: {
          id: true,
          formId: true,
          formName: true,
          tableName: true,
          isActive: true,
          createdAt: true
        }
      })
      console.log('前3个表单配置:')
      forms.forEach(form => {
        console.log(`  - ID: ${form.id}, 表单ID: ${form.formId}, 名称: ${form.formName}, 表名: ${form.tableName}, 活跃: ${form.isActive}`)
      })
    }
    console.log('')

    // 4. 检查系统日志
    console.log('4. 检查系统日志...')
    const logCount = await prisma.systemLog.count()
    console.log(`系统日志总数: ${logCount}`)
    
    if (logCount > 0) {
      const logs = await prisma.systemLog.findMany({
        take: 3,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          action: true,
          resource: true,
          userId: true,
          createdAt: true
        }
      })
      console.log('最近3条日志:')
      logs.forEach(log => {
        console.log(`  - ID: ${log.id}, 动作: ${log.action}, 资源: ${log.resource}, 用户ID: ${log.userId}, 时间: ${log.createdAt}`)
      })
    }
    console.log('')

    // 5. 检查表单数据表
    console.log('5. 检查表单数据表...')
    const activeForms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true, formName: true }
    })
    
    console.log(`活跃表单数量: ${activeForms.length}`)
    
    for (const form of activeForms) {
      if (form.tableName) {
        try {
          const result = await prisma.$queryRawUnsafe(
            `SELECT COUNT(*) as count FROM \`${form.tableName}\``
          )
          const count = result[0]?.count || 0
          console.log(`  - ${form.formName} (${form.tableName}): ${count} 条记录`)
        } catch (error) {
          console.log(`  - ${form.formName} (${form.tableName}): 表不存在或查询失败`)
        }
      }
    }
    console.log('')

    // 6. 检查今日数据
    console.log('6. 检查今日数据...')
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    const todayLogs = await prisma.systemLog.count({
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    })
    console.log(`今日系统日志: ${todayLogs} 条`)
    
    for (const form of activeForms) {
      if (form.tableName) {
        try {
          const result = await prisma.$queryRawUnsafe(
            `SELECT COUNT(*) as count FROM \`${form.tableName}\` WHERE created_at >= ? AND created_at < ?`,
            today,
            tomorrow
          )
          const count = result[0]?.count || 0
          console.log(`  - ${form.formName} 今日新增: ${count} 条`)
        } catch (error) {
          console.log(`  - ${form.formName} 今日新增: 查询失败`)
        }
      }
    }

    console.log('\n🎉 数据库检查完成!')

  } catch (error) {
    console.error('❌ 数据库检查失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkDatabase()
