const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

// 模拟仪表板API的逻辑
async function testDashboardAPI() {
  try {
    console.log('🔍 测试仪表板API逻辑...\n')

    const userId = 1 // 假设是admin用户
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    console.log(`今日时间范围: ${today.toISOString()} 到 ${tomorrow.toISOString()}\n`)

    // 检查用户角色
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    })

    const isAdmin = currentUser?.role === 'admin'
    console.log(`用户角色: ${currentUser?.role}, 是否管理员: ${isAdmin}\n`)

    if (isAdmin) {
      console.log('=== 管理员统计数据 ===')
      
      // 用户统计
      const totalUsers = await prisma.user.count()
      const activeUsers = await prisma.user.count({ where: { is_active: true } })
      console.log(`用户统计: 总数 ${totalUsers}, 活跃 ${activeUsers}`)

      // 表单统计
      const totalForms = await prisma.formConfig.count()
      const activeForms = await prisma.formConfig.count({ where: { isActive: true } })
      console.log(`表单统计: 总数 ${totalForms}, 活跃 ${activeForms}`)

      // 数据统计
      const totalRecords = await getTotalRecords()
      const todayRecords = await getTodayRecords(today, tomorrow)
      console.log(`数据统计: 总记录 ${totalRecords}, 今日新增 ${todayRecords}`)

      // Webhook统计
      const webhookSuccess = await prisma.systemLog.count({
        where: {
          action: 'WEBHOOK_RECEIVED',
          createdAt: { gte: today },
        },
      })
      const webhookErrors = await prisma.systemLog.count({
        where: {
          action: 'WEBHOOK_ERROR',
          createdAt: { gte: today },
        },
      })
      console.log(`Webhook统计: 成功 ${webhookSuccess}, 错误 ${webhookErrors}`)

      const stats = {
        totalUsers,
        activeUsers,
        totalForms,
        activeForms,
        totalRecords,
        todayRecords,
        webhookSuccess,
        webhookErrors,
      }

      console.log('\n最终统计结果:')
      console.log(JSON.stringify(stats, null, 2))
    }

    // 获取最近活动
    console.log('\n=== 最近活动 ===')
    const recentActivities = await prisma.systemLog.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      where: isAdmin ? {} : { userId },
      include: {
        user: {
          select: {
            username: true,
            doctor_name: true,
          },
        },
      },
    })

    console.log(`最近活动数量: ${recentActivities.length}`)
    recentActivities.forEach((log, index) => {
      console.log(`${index + 1}. ${log.action} - ${log.resource} - ${log.createdAt}`)
    })

    // 获取表单统计
    if (isAdmin) {
      console.log('\n=== 表单统计 ===')
      const forms = await prisma.formConfig.findMany({
        where: { isActive: true },
        select: {
          formId: true,
          formName: true,
          tableName: true,
          isActive: true,
        },
      })

      console.log(`活跃表单数量: ${forms.length}`)
      
      for (const form of forms) {
        console.log(`\n处理表单: ${form.formName} (${form.tableName})`)
        
        const recordCount = await getTableRecordCount(form.tableName)
        const todayCount = await getTableTodayCount(form.tableName, today, tomorrow)
        
        console.log(`  - 总记录: ${recordCount}`)
        console.log(`  - 今日新增: ${todayCount}`)
      }
    }

    console.log('\n🎉 API测试完成!')

  } catch (error) {
    console.error('❌ API测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 获取所有数据表的总记录数
async function getTotalRecords() {
  try {
    const forms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true },
    })

    let total = 0
    for (const form of forms) {
      if (form.tableName) {
        const count = await getTableRecordCount(form.tableName)
        total += count
      }
    }

    return total
  } catch (error) {
    console.error('获取总记录数失败:', error)
    return 0
  }
}

// 获取今日新增记录数
async function getTodayRecords(today, tomorrow) {
  try {
    const forms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true },
    })

    let total = 0
    for (const form of forms) {
      if (form.tableName) {
        const count = await getTableTodayCount(form.tableName, today, tomorrow)
        total += count
      }
    }

    return total
  } catch (error) {
    console.error('获取今日记录数失败:', error)
    return 0
  }
}

// 获取单个表的记录数
async function getTableRecordCount(tableName) {
  try {
    // 先检查表是否存在
    const tableExists = await checkTableExists(tableName)
    if (!tableExists) {
      console.warn(`表 ${tableName} 不存在`)
      return 0
    }

    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM \`${tableName}\``
    )
    return Number(result[0]?.count || 0)
  } catch (error) {
    console.error(`获取表 ${tableName} 记录数失败:`, error)
    return 0
  }
}

// 获取单个表的今日记录数
async function getTableTodayCount(tableName, today, tomorrow) {
  try {
    // 先检查表是否存在
    const tableExists = await checkTableExists(tableName)
    if (!tableExists) {
      console.warn(`表 ${tableName} 不存在`)
      return 0
    }

    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM \`${tableName}\` WHERE created_at >= ? AND created_at < ?`,
      today,
      tomorrow
    )
    return Number(result[0]?.count || 0)
  } catch (error) {
    console.error(`获取表 ${tableName} 今日记录数失败:`, error)
    return 0
  }
}

// 检查表是否存在
async function checkTableExists(tableName) {
  try {
    const result = await prisma.$queryRawUnsafe(
      `SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?`,
      tableName
    )
    return Number(result[0]?.count || 0) > 0
  } catch (error) {
    console.error(`检查表 ${tableName} 是否存在失败:`, error)
    return false
  }
}

testDashboardAPI()
