import type { ThemeConfig } from 'antd'

export const antdTheme: ThemeConfig = {
  token: {
    // 主色调配置
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',

    // 中性色配置
    colorTextBase: '#000000d9',
    colorBgBase: '#ffffff',

    // 边框和圆角
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,

    // 字体配置
    fontSize: 14,
    fontSizeHeading1: 38,
    fontSizeHeading2: 30,
    fontSizeHeading3: 24,
    fontSizeHeading4: 20,
    fontSizeHeading5: 16,
    fontSizeLG: 16,
    fontSizeSM: 12,
    fontSizeXL: 20,

    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontWeightStrong: 600,

    // 行高配置
    lineHeight: 1.5714285714285714,
    lineHeightLG: 1.5,
    lineHeightSM: 1.66,

    // 控件尺寸
    controlHeight: 32,
    controlHeightLG: 40,
    controlHeightSM: 24,

    // 间距配置
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    paddingXXS: 4,

    margin: 16,
    marginLG: 24,
    marginSM: 12,
    marginXS: 8,
    marginXXS: 4,

    // 阴影配置
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    boxShadowSecondary: '0 4px 12px rgba(0, 0, 0, 0.15)',
    boxShadowTertiary:
      '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
  },
  components: {
    // Layout 组件配置
    Layout: {
      siderBg: '#001529',
      headerBg: '#ffffff',
      bodyBg: '#f0f2f5',
      footerBg: '#ffffff',
      triggerBg: '#002140',
      triggerColor: '#ffffff',
      zeroTriggerWidth: 48,
      zeroTriggerHeight: 42,
    },

    // Menu 组件配置
    Menu: {
      darkItemBg: '#001529',
      darkItemSelectedBg: '#1890ff',
      darkItemHoverBg: '#1c2433',
      darkSubMenuItemBg: '#000c17',
      darkItemColor: 'rgba(255, 255, 255, 0.65)',
      darkItemSelectedColor: '#ffffff',
      darkItemHoverColor: '#ffffff',
      itemHeight: 40,
      collapsedWidth: 80,
    },

    // Button 组件配置
    Button: {
      controlHeight: 32,
      controlHeightLG: 40,
      controlHeightSM: 24,
      paddingContentHorizontal: 16,
      paddingContentHorizontalLG: 16,
      paddingContentHorizontalSM: 8,
      fontWeight: 400,
      borderRadius: 6,
      primaryShadow: '0 2px 0 rgba(5, 145, 255, 0.1)',
    },

    // Input 组件配置
    Input: {
      controlHeight: 32,
      controlHeightLG: 40,
      controlHeightSM: 24,
      paddingBlock: 4,
      paddingInline: 12,
      borderRadius: 6,
      activeBorderColor: '#1890ff',
      hoverBorderColor: '#40a9ff',
    },

    // Table 组件配置
    Table: {
      headerBg: '#fafafa',
      headerColor: '#000000d9',
      headerSortActiveBg: '#f2f2f2',
      headerSortHoverBg: '#f5f5f5',
      bodySortBg: '#fafafa',
      rowHoverBg: '#f5f5f5',
      rowSelectedBg: '#e6f7ff',
      rowSelectedHoverBg: '#dcf4ff',
      cellPaddingBlock: 12,
      cellPaddingInline: 16,
      borderColor: '#f0f0f0',
      headerSplitColor: '#f0f0f0',
    },

    // Card 组件配置
    Card: {
      headerBg: 'transparent',
      colorBorderSecondary: '#f0f0f0',
      borderRadiusLG: 8,
      paddingLG: 24,
    },

    // Form 组件配置
    Form: {
      labelColor: '#000000d9',
      labelFontSize: 14,
      labelHeight: 32,
      itemMarginBottom: 24,
      verticalLabelPadding: '0 0 8px',
      verticalLabelMargin: 0,
    },

    // Modal 组件配置
    Modal: {
      borderRadiusLG: 8,
      headerBg: '#ffffff',
      contentBg: '#ffffff',
      titleColor: '#000000d9',
      titleFontSize: 16,
    },

    // Notification 组件配置
    Notification: {
      borderRadiusLG: 8,
      paddingMD: 16,
    },

    // Message 组件配置
    Message: {
      borderRadiusLG: 8,
      paddingMD: 12,
    },

    // Dropdown 组件配置
    Dropdown: {
      borderRadiusLG: 8,
      paddingBlock: 8,
    },

    // Tooltip 组件配置
    Tooltip: {
      borderRadius: 6,
    },

    // Breadcrumb 组件配置
    Breadcrumb: {
      itemColor: 'rgba(0, 0, 0, 0.45)',
      lastItemColor: '#000000d9',
      linkColor: 'rgba(0, 0, 0, 0.45)',
      linkHoverColor: '#1890ff',
      separatorColor: 'rgba(0, 0, 0, 0.45)',
      fontSize: 14,
    },

    // Avatar 组件配置
    Avatar: {
      borderRadius: 6,
      colorTextPlaceholder: '#ffffff',
    },

    // Badge 组件配置
    Badge: {
      borderRadiusSM: 4,
      fontSizeSM: 12,
    },

    // Tag 组件配置
    Tag: {
      borderRadiusSM: 4,
      fontSize: 12,
      lineHeight: 1.5,
    },

    // Progress 组件配置
    Progress: {
      borderRadius: 100,
      lineBorderRadius: 100,
    },

    // Switch 组件配置
    Switch: {
      borderRadius: 100,
      trackMinWidth: 44,
      trackHeight: 22,
      trackPadding: 2,
      handleSize: 18,
    },

    // Slider 组件配置
    Slider: {
      borderRadius: 6,
      borderRadiusXS: 3,
      handleSize: 14,
      handleSizeHover: 16,
      trackBg: '#f5f5f5',
      trackHoverBg: '#e1e1e1',
      railBg: '#f5f5f5',
      railHoverBg: '#e1e1e1',
    },
  },
}

// 暗色主题配置 (可选)
export const darkTheme: ThemeConfig = {
  ...antdTheme,
  token: {
    ...antdTheme.token,
    colorTextBase: '#ffffff',
    colorBgBase: '#141414',
  },
  components: {
    ...antdTheme.components,
    Layout: {
      ...antdTheme.components?.Layout,
      headerBg: '#141414',
      bodyBg: '#000000',
      footerBg: '#141414',
    },
  },
}
