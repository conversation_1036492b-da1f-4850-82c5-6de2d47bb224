# 肺功能数据管理平台 - 部署指南

## 📋 目录

- [概述](#概述)
- [系统要求](#系统要求)
- [部署方式选择](#部署方式选择)
- [Docker部署](#docker部署推荐)
- [传统部署](#传统部署)
- [腾讯云部署](#腾讯云部署)
- [环境配置](#环境配置)
- [数据库配置](#数据库配置)
- [SSL配置](#ssl配置)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 概述

本文档详细说明如何在不同环境中部署肺功能数据管理平台，包括Docker容器化部署和传统部署方式。

## 系统要求

### 最低配置

- **CPU**: 2核心
- **内存**: 4GB RAM
- **磁盘**: 20GB 可用空间
- **操作系统**: Linux (推荐 Ubuntu 20.04+)

### 推荐配置

- **CPU**: 4核心
- **内存**: 8GB RAM
- **磁盘**: 50GB SSD
- **操作系统**: Ubuntu 22.04 LTS

### 软件依赖

- Docker 20.10+ 和 Docker Compose 2.0+ (Docker部署)
- Node.js 18+、PM2、Nginx (传统部署)
- MySQL 8.0+ (数据库)

## 部署方式选择

### 🐳 Docker部署（推荐用于快速部署）

**优势**：
- 环境隔离，避免依赖冲突
- 一键部署，简化运维
- 易于管理和扩展
- 跨平台兼容性好

**适用场景**：
- 快速部署和测试
- 开发环境
- 容器化基础设施

### 🚀 传统部署（推荐用于生产环境）

**优势**：
- 性能更好，资源占用少
- 调试方便，配置灵活
- 更好的系统集成
- 精细的资源控制

**适用场景**：
- 生产环境
- 性能要求高的场景
- 需要精细控制的环境

## Docker部署（推荐）

### 1. 快速开始

```bash
# 克隆项目
git clone https://github.com/peckbyte/free_lung_function_project_admin.git
cd free_lung_function_project_admin

# 配置环境变量
cp .env.example .env.production
nano .env.production

# 一键部署
docker-compose up -d
```

### 2. 详细步骤

#### 2.1 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker
```

#### 2.2 项目部署

```bash
# 创建项目目录
sudo mkdir -p /opt/lung-function-admin
cd /opt/lung-function-admin

# 克隆项目
git clone https://github.com/peckbyte/free_lung_function_project_admin.git .

# 配置环境变量
cp .env.example .env.production
```

#### 2.3 环境变量配置

编辑 `.env.production` 文件：

```env
# 数据库配置
DATABASE_URL="mysql://username:password@host:port/database"

# 应用配置
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-secret-key-here"

# 端口配置
PORT=3011
```

#### 2.4 启动服务

```bash
# 构建并启动容器
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

#### 2.5 初始化数据库

```bash
# 运行数据库迁移
docker-compose exec app npx prisma db push

# 创建管理员用户
docker-compose exec app node scripts/create-admin.js
```

### 3. Docker管理命令

```bash
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 更新应用
git pull
docker-compose build --no-cache
docker-compose up -d

# 查看资源使用
docker stats

# 清理未使用的镜像
docker system prune -f
```

## 传统部署

### 1. 一键部署脚本（推荐）

```bash
# 下载并执行一键部署脚本
curl -fsSL https://raw.githubusercontent.com/peckbyte/free_lung_function_project_admin/main/scripts/deploy-traditional.sh | bash

# 或者手动下载执行
wget https://raw.githubusercontent.com/peckbyte/free_lung_function_project_admin/main/scripts/deploy-traditional.sh
chmod +x deploy-traditional.sh
./deploy-traditional.sh
```

**脚本功能**：
- ✅ 自动检查系统要求
- ✅ 安装Node.js 18+、Yarn、PM2、Nginx
- ✅ 克隆项目代码并安装依赖
- ✅ 配置PM2进程管理
- ✅ 配置Nginx反向代理
- ✅ 设置开机自启动

### 2. 手动部署步骤

#### 2.1 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git build-essential

# 安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装Yarn
npm install -g yarn

# 安装PM2
npm install -g pm2

# 安装Nginx
sudo apt install -y nginx
```

#### 2.2 项目部署

```bash
# 创建项目目录
sudo mkdir -p ~/www/wwwroot
cd ~/www/wwwroot

# 克隆项目
git clone https://github.com/peckbyte/free_lung_function_project_admin.git free_lung_function_project
cd free_lung_function_project

# 安装依赖
yarn install

# 构建项目
yarn build
```

#### 2.3 配置PM2

```bash
# 配置环境变量
cp .env.example .env.production
nano .env.production

# 启动PM2应用
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

#### 2.4 配置Nginx

创建Nginx配置文件：

```bash
sudo nano /etc/nginx/sites-available/lung-function-admin
```

配置内容：

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://localhost:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

启用配置：

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/lung-function-admin /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 腾讯云部署

### 前提条件

- 腾讯云CVM (2核4GB Ubuntu 20.04 或更高版本)
- 安全组开放3011端口和80端口
- 腾讯云轻量数据库已配置 (MySQL)
- 确保已有GitHub账号可访问项目代码

### 腾讯云特殊配置

#### 1. 安全组配置

在腾讯云控制台配置安全组规则：

```
入站规则：
- HTTP: 80端口，来源：0.0.0.0/0
- HTTPS: 443端口，来源：0.0.0.0/0
- 自定义: 3011端口，来源：0.0.0.0/0
- SSH: 22端口，来源：你的IP
```

#### 2. 数据库连接

腾讯云轻量数据库连接配置：

```env
# 开发环境 (公网连接)
DATABASE_URL="mysql://srmyy_123:<EMAIL>:23387/srmyy_123"

# 生产环境 (内网连接，推荐)
DATABASE_URL="mysql://srmyy_123:password@*********:3306/srmyy_123"
```

#### 3. 域名和SSL配置

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 申请SSL证书
sudo certbot --nginx -d yourdomain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## 环境配置

### 必需的环境变量

```env
# 数据库连接
DATABASE_URL="mysql://username:password@host:port/database"

# NextAuth配置
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-secret-key-here"

# 应用配置
PORT=3011
NODE_ENV=production

# 文件上传配置
UPLOAD_DIR="/path/to/uploads"
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=info
LOG_FILE="/var/log/lung-function-admin.log"
```

### 可选的环境变量

```env
# 邮件配置
SMTP_HOST="smtp.example.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-password"

# Redis缓存 (可选)
REDIS_URL="redis://localhost:6379"

# 监控配置
SENTRY_DSN="your-sentry-dsn"
```

## 数据库配置

### MySQL配置优化

```sql
-- 创建数据库
CREATE DATABASE lung_function_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'lung_admin'@'%' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON lung_function_admin.* TO 'lung_admin'@'%';
FLUSH PRIVILEGES;
```

### 性能优化配置

在 `/etc/mysql/mysql.conf.d/mysqld.cnf` 中添加：

```ini
[mysqld]
# 连接配置
max_connections = 200
max_connect_errors = 10

# 缓存配置
innodb_buffer_pool_size = 1G
query_cache_size = 64M
query_cache_type = 1

# 日志配置
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

## SSL配置

### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 申请证书
sudo certbot --nginx -d yourdomain.com

# 验证自动续期
sudo certbot renew --dry-run
```

### 手动SSL配置

如果使用自己的SSL证书：

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}
```

## 监控和维护

### 系统监控

```bash
# 查看应用状态
pm2 status

# 查看应用日志
pm2 logs

# 查看系统资源
htop
df -h
free -h

# 查看Nginx状态
sudo systemctl status nginx

# 查看数据库状态
sudo systemctl status mysql
```

### 日志管理

```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/lung-function-admin

# 内容：
/var/log/lung-function-admin/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload all
    endscript
}
```

### 备份策略

```bash
# 数据库备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u username -p database_name > /backup/db_backup_$DATE.sql
gzip /backup/db_backup_$DATE.sql

# 文件备份
tar -czf /backup/files_backup_$DATE.tar.gz /path/to/uploads

# 清理旧备份 (保留30天)
find /backup -name "*.gz" -mtime +30 -delete
```

## 故障排除

### 常见问题

#### 1. 应用无法启动

```bash
# 检查端口占用
sudo netstat -tlnp | grep :3011

# 检查PM2日志
pm2 logs

# 检查环境变量
pm2 env 0
```

#### 2. 数据库连接失败

```bash
# 测试数据库连接
mysql -h host -P port -u username -p database

# 检查数据库服务状态
sudo systemctl status mysql

# 查看数据库错误日志
sudo tail -f /var/log/mysql/error.log
```

#### 3. Nginx配置问题

```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 重新加载配置
sudo nginx -s reload
```

#### 4. SSL证书问题

```bash
# 检查证书有效期
sudo certbot certificates

# 手动续期
sudo certbot renew

# 测试SSL配置
openssl s_client -connect yourdomain.com:443
```

### 性能优化

#### 1. 应用层优化

```bash
# 启用PM2集群模式
pm2 start ecosystem.config.js --env production -i max

# 配置内存限制
pm2 start app.js --max-memory-restart 1G
```

#### 2. 数据库优化

```sql
-- 添加索引
CREATE INDEX idx_created_at ON your_table(created_at);
CREATE INDEX idx_user_id ON your_table(user_id);

-- 分析查询性能
EXPLAIN SELECT * FROM your_table WHERE condition;
```

#### 3. Nginx优化

```nginx
# 启用Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript;

# 启用缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 更新和维护

#### 应用更新流程

```bash
# 1. 备份当前版本
cp -r /current/app /backup/app_$(date +%Y%m%d)

# 2. 拉取最新代码
git pull origin main

# 3. 安装依赖
yarn install

# 4. 构建应用
yarn build

# 5. 运行数据库迁移
npx prisma db push

# 6. 重启应用
pm2 reload all

# 7. 验证部署
curl -f http://localhost:3011/api/health
```

#### 回滚流程

```bash
# 1. 停止当前应用
pm2 stop all

# 2. 恢复备份
rm -rf /current/app
cp -r /backup/app_YYYYMMDD /current/app

# 3. 重启应用
pm2 start all

# 4. 验证回滚
curl -f http://localhost:3011/api/health
```

---

## 📞 支持

如果在部署过程中遇到问题：

1. 📖 查看[故障排除](#故障排除)部分
2. 🐛 提交[Issue](../../issues)
3. 💬 参与[讨论](../../discussions)
4. 📧 联系技术支持

---

**部署成功后，请访问您的域名验证应用是否正常运行！**
