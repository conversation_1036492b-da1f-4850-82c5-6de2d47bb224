'use client'

import { useState } from 'react'
import {
  Card,
  Typo<PERSON>,
  Button,
  Alert,
  Input,
  Space,
  message,
  Collapse,
} from 'antd'
import {
  FileTextOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ArrowRightOutlined,
  CopyOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography
const { TextArea } = Input
const { Panel } = Collapse

interface JsonInputStepProps {
  onSubmit: (jsonData: any) => void
  initialValue?: any
}

// 示例JSON数据
const exampleJson = {
  form: 'ZFs2eo',
  form_name: '预约免费肺功能检查、免费办理慢性病医保',
  entry: {
    serial_number: 123,
    field_1: '张三',
    field_2: '选项1',
    field_6: 123,
    field_7: '选项1',
    field_3: '13812345678',
    field_4: ['选项1', '选项2', '选项3'],
    x_field_1: '这是一行文字',
    color_mark: '深绿色',
    creator_name: '小王',
    created_at: '2025-06-29T05:16:12.175Z',
    updated_at: '2025-06-29T05:16:12.175Z',
    info_filling_duration: 123,
    info_platform: 'Macintosh',
    info_os: 'OS X 10.13.6',
    info_browser: 'Chrome 68.0.3440.106',
    info_region: {
      province: '陕西省',
      city: '西安市',
      district: '雁塔区',
      street: '高新路',
    },
    info_remote_ip: '127.0.0.1',
  },
}

export function JsonInputStep({ onSubmit, initialValue }: JsonInputStepProps) {
  const [jsonText, setJsonText] = useState(
    initialValue ? JSON.stringify(initialValue, null, 2) : ''
  )
  const [parsedJson, setParsedJson] = useState<any>(initialValue || null)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const handleJsonChange = (value: string) => {
    setJsonText(value)
    setError('')

    if (!value.trim()) {
      setParsedJson(null)
      return
    }

    try {
      const parsed = JSON.parse(value)
      setParsedJson(parsed)
    } catch (err) {
      setParsedJson(null)
      setError('JSON格式不正确，请检查语法')
    }
  }

  const validateJsonStructure = (json: any): string | null => {
    if (!json || typeof json !== 'object') {
      return 'JSON必须是一个对象'
    }

    if (!json.form || typeof json.form !== 'string') {
      return '缺少必要字段: form (表单ID)'
    }

    if (!json.form_name || typeof json.form_name !== 'string') {
      return '缺少必要字段: form_name (表单名称)'
    }

    if (!json.entry || typeof json.entry !== 'object') {
      return '缺少必要字段: entry (表单数据)'
    }

    if (
      !json.entry.serial_number ||
      typeof json.entry.serial_number !== 'number'
    ) {
      return '缺少必要字段: entry.serial_number (序列号)'
    }

    return null
  }

  const handleSubmit = () => {
    if (!parsedJson) {
      message.error('请先输入有效的JSON数据')
      return
    }

    const validationError = validateJsonStructure(parsedJson)
    if (validationError) {
      setError(validationError)
      return
    }

    setLoading(true)
    setTimeout(() => {
      onSubmit(parsedJson)
      setLoading(false)
    }, 500)
  }

  const handleUseExample = () => {
    const exampleText = JSON.stringify(exampleJson, null, 2)
    setJsonText(exampleText)
    handleJsonChange(exampleText)
  }

  const copyExample = () => {
    navigator.clipboard.writeText(JSON.stringify(exampleJson, null, 2))
    message.success('示例JSON已复制到剪贴板')
  }

  const renderJsonPreview = () => {
    if (!parsedJson) return null

    const { entry } = parsedJson
    const fields = Object.keys(entry).filter(
      key => key.startsWith('field_') || key.startsWith('x_field_')
    )

    return (
      <Card size="small" title="解析结果预览" className="mt-4">
        <div className="space-y-2">
          <div>
            <Text strong>表单ID: </Text>
            <Text code>{parsedJson.form}</Text>
          </div>
          <div>
            <Text strong>表单名称: </Text>
            <Text>{parsedJson.form_name}</Text>
          </div>
          <div>
            <Text strong>检测到的字段数量: </Text>
            <Text type="success">{fields.length} 个</Text>
          </div>
          <div>
            <Text strong>字段列表: </Text>
            <div className="mt-1">
              {fields.map(field => (
                <span
                  key={field}
                  className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2 mb-1"
                >
                  {field}: {typeof entry[field]}
                </span>
              ))}
            </div>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <Title level={4} className="mb-4">
          <FileTextOutlined className="mr-2" />
          输入金数据JSON样例
        </Title>

        <Alert
          message="说明"
          description="请粘贴一个完整的金数据Webhook推送JSON数据样例。系统将根据这个样例自动识别字段结构并生成字段映射配置。"
          type="info"
          showIcon
          className="mb-4"
        />

        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <Text strong>JSON数据:</Text>
              <Space>
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={copyExample}
                >
                  复制示例
                </Button>
                <Button size="small" type="link" onClick={handleUseExample}>
                  使用示例数据
                </Button>
              </Space>
            </div>

            <TextArea
              value={jsonText}
              onChange={e => handleJsonChange(e.target.value)}
              placeholder="请粘贴金数据Webhook JSON数据..."
              rows={15}
              className="font-mono text-sm"
            />
          </div>

          {error && (
            <Alert
              message="JSON解析错误"
              description={error}
              type="error"
              showIcon
              icon={<ExclamationCircleOutlined />}
            />
          )}

          {parsedJson && !error && (
            <Alert
              message="JSON解析成功"
              description="JSON格式正确，已成功解析数据结构"
              type="success"
              showIcon
              icon={<CheckCircleOutlined />}
            />
          )}

          {renderJsonPreview()}

          <div className="flex justify-end">
            <Button
              type="primary"
              size="large"
              onClick={handleSubmit}
              disabled={!parsedJson || !!error}
              loading={loading}
              icon={<ArrowRightOutlined />}
            >
              解析完成，下一步
            </Button>
          </div>
        </div>
      </Card>

      {/* 示例JSON展示 */}
      <Collapse ghost>
        <Panel header="查看JSON数据格式说明" key="1">
          <Card size="small">
            <Text strong className="block mb-2">
              标准金数据Webhook JSON结构:
            </Text>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
              {`{
  "form": "表单ID",
  "form_name": "表单名称", 
  "entry": {
    "serial_number": 序列号,
    "field_1": "字段1值",
    "field_2": "字段2值",
    "field_3": "字段3值",
    "x_field_1": "扩展字段值",
    "created_at": "创建时间",
    "updated_at": "更新时间",
    "info_region": {
      "province": "省份",
      "city": "城市"
    }
  }
}`}
            </pre>
            <div className="mt-3 space-y-1 text-sm text-gray-600">
              <div>
                • <Text code>form</Text>: 金数据表单的唯一标识
              </div>
              <div>
                • <Text code>form_name</Text>: 表单显示名称
              </div>
              <div>
                • <Text code>entry</Text>: 包含所有表单字段数据
              </div>
              <div>
                • <Text code>field_*</Text>: 普通表单字段
              </div>
              <div>
                • <Text code>x_field_*</Text>: 扩展字段
              </div>
              <div>
                • <Text code>info_*</Text>: 系统自动生成的元数据
              </div>
            </div>
          </Card>
        </Panel>
      </Collapse>
    </div>
  )
}
