import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  insertDataToDynamicTable,
  fixTableNumberFields,
} from '@/lib/dynamicTable'
import { z } from 'zod'

// 记录验证失败详情的辅助函数
async function recordValidationFailure(
  formId: string,
  originalData: any,
  errorType: string,
  errorMessage: string,
  validationErrors: any,
  expectedFormat?: any,
  suggestions?: any,
  clientIp: string = 'unknown',
  userAgent: string = 'unknown'
) {
  try {
    await prisma.webhookValidationFailure.create({
      data: {
        formId,
        originalData,
        validationErrors,
        errorType,
        errorMessage,
        expectedFormat,
        suggestions,
        ipAddress: clientIp,
        userAgent,
      },
    })
  } catch (error) {
    console.error('记录验证失败详情时出错:', error)
  }
}

// 金数据Webhook数据验证schema
const jinshujuWebhookSchema = z.object({
  form: z.string(),
  form_name: z.string(),
  entry: z
    .object({
      serial_number: z.number(),
    })
    .passthrough(),
})

// POST /api/webhook/[formId] - 接收金数据Webhook推送
export async function POST(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  const formId = params.formId
  const clientIp =
    request.headers.get('x-forwarded-for') ||
    request.headers.get('x-real-ip') ||
    request.headers.get('remote-addr') ||
    'unknown'

  try {
    // 获取请求body
    const body = await request.json()
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // 验证JSON结构
    const validation = jinshujuWebhookSchema.safeParse(body)
    if (!validation.success) {
      console.error('Webhook数据验证失败:', validation.error.issues)

      // 记录Schema验证失败详情
      await recordValidationFailure(
        formId,
        body,
        'SCHEMA_VALIDATION',
        '数据结构不符合金数据Webhook格式要求',
        validation.error.issues,
        {
          form: 'string (必填)',
          form_name: 'string (必填)',
          entry: {
            serial_number: 'number (必填)',
            '...其他字段': 'any',
          },
        },
        {
          fix: '请检查金数据推送设置，确保包含必要的form、form_name和entry.serial_number字段',
          common_issues: [
            '缺少form字段',
            'serial_number不是数字类型',
            '缺少entry对象',
          ],
        },
        clientIp,
        userAgent
      )

      return NextResponse.json(
        {
          success: false,
          error: 'Invalid webhook data format',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const webhookData = validation.data

    // 验证表单ID是否匹配
    if (webhookData.form !== formId) {
      console.error(`表单ID不匹配: 期望 ${formId}, 实际 ${webhookData.form}`)

      // 记录表单ID不匹配详情
      await recordValidationFailure(
        formId,
        body,
        'FORM_ID_MISMATCH',
        `表单ID不匹配: 期望 ${formId}, 实际 ${webhookData.form}`,
        {
          expected: formId,
          actual: webhookData.form,
          mismatch_type: 'form_id',
        },
        {
          correct_form_id: formId,
        },
        {
          fix: `请检查金数据表单设置，确保Webhook URL中的表单ID为 ${formId}`,
          note: '表单ID必须与URL路径中的formId参数匹配',
        },
        clientIp,
        userAgent
      )

      return NextResponse.json(
        {
          success: false,
          error: 'Form ID mismatch',
        },
        { status: 400 }
      )
    }

    // 查找对应的表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isActive: true },
    })

    if (!formConfig) {
      console.error(`表单配置不存在或已禁用: ${formId}`)

      // 记录表单配置不存在详情
      await recordValidationFailure(
        formId,
        body,
        'FORM_CONFIG_NOT_FOUND',
        `表单配置不存在或已禁用: ${formId}`,
        {
          form_id: formId,
          config_status: 'not_found_or_disabled',
        },
        null,
        {
          fix: '请在管理后台创建并激活此表单的配置',
          steps: [
            '1. 进入表单配置页面',
            '2. 创建新的表单配置',
            '3. 确保表单ID正确',
            '4. 激活表单配置',
          ],
        },
        clientIp,
        userAgent
      )

      return NextResponse.json(
        {
          success: false,
          error: 'Form configuration not found or disabled',
        },
        { status: 404 }
      )
    }

    // 检查是否为重复数据（基于serial_number）
    const tableName = formConfig.tableName
    if (!tableName) {
      console.error(`表单配置缺少表名: ${formId}`)

      // 记录表名未配置详情
      await recordValidationFailure(
        formId,
        body,
        'TABLE_NAME_NOT_CONFIGURED',
        `表单配置缺少表名: ${formId}`,
        {
          form_id: formId,
          table_name: null,
          config_incomplete: true,
        },
        null,
        {
          fix: '请完善表单配置，设置正确的数据表名',
          steps: [
            '1. 编辑表单配置',
            '2. 重新生成或设置表名',
            '3. 确保数据表已创建',
          ],
        },
        clientIp,
        userAgent
      )

      return NextResponse.json(
        {
          success: false,
          error: 'Form table name not configured',
        },
        { status: 400 }
      )
    }

    const serialNumber = webhookData.entry.serial_number

    const existingRecord = await prisma.$queryRawUnsafe<Array<{ id: bigint }>>(
      `SELECT id FROM \`${tableName}\` WHERE serial_number = ? LIMIT 1`,
      serialNumber
    )

    if (existingRecord.length > 0) {
      console.log(`数据已存在，跳过插入: ${formId}#${serialNumber}`)
      return NextResponse.json({
        success: true,
        message: 'Data already exists, skipped',
        recordId: existingRecord[0].id.toString(),
      })
    }

    // 准备插入数据
    const insertData = {
      ...webhookData.entry,
      source_ip: clientIp,
      creator_name: webhookData.entry.creator_name || null,
    }

    // 检查并修复表结构中的数字字段（如果需要）
    const fixResult = await fixTableNumberFields(
      tableName,
      formConfig.fieldMapping as Record<string, any>
    )

    if (!fixResult.success) {
      console.warn(`修复表结构失败，但继续尝试插入数据: ${fixResult.error}`)
    }

    // 插入数据到动态表
    const insertResult = await insertDataToDynamicTable(
      tableName,
      insertData,
      formConfig.fieldMapping as Record<string, any>
    )

    if (!insertResult.success) {
      // 记录数据插入失败详情
      await recordValidationFailure(
        formId,
        body,
        'DATA_INSERT_FAILED',
        `数据插入失败: ${insertResult.error}`,
        {
          insert_error: insertResult.error,
          table_name: tableName,
          serial_number: serialNumber,
          field_mapping: formConfig.fieldMapping,
        },
        {
          required_fields: Object.keys(
            formConfig.fieldMapping as Record<string, any>
          ),
          table_structure: '请检查数据表结构',
        },
        {
          fix: '请检查字段映射配置和数据表结构',
          common_issues: [
            '字段类型不匹配',
            '必填字段缺失',
            '字段长度超限',
            '数据表结构与配置不符',
          ],
          steps: [
            '1. 检查字段映射配置',
            '2. 验证数据表结构',
            '3. 确认数据类型匹配',
            '4. 检查字段约束条件',
          ],
        },
        clientIp,
        userAgent
      )

      throw new Error(`数据插入失败: ${insertResult.error}`)
    }

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: null, // Webhook调用无用户ID
        action: 'WEBHOOK_RECEIVED',
        resource: 'WebhookData',
        resourceId: insertResult.id!.toString(),
        details: {
          formId,
          serialNumber,
          tableName,
          clientIp,
          dataSize: JSON.stringify(webhookData).length,
        },
        ipAddress: clientIp,
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    console.log(
      `Webhook数据处理成功: ${formId}#${serialNumber}, 记录ID: ${insertResult.id}`
    )

    return NextResponse.json({
      success: true,
      message: 'Webhook data processed successfully',
      recordId: insertResult.id?.toString(),
      formId,
      serialNumber,
    })
  } catch (error) {
    console.error(`Webhook处理失败 [${formId}]:`, error)

    // 如果不是已知的验证错误，记录为通用处理失败
    try {
      const body = await request.json().catch(() => ({}))
      await recordValidationFailure(
        formId,
        body,
        'WEBHOOK_PROCESSING_ERROR',
        error instanceof Error
          ? error.message
          : 'Unknown webhook processing error',
        {
          error_type: 'unexpected_error',
          error_message:
            error instanceof Error ? error.message : 'Unknown error',
          stack_trace: error instanceof Error ? error.stack : undefined,
        },
        null,
        {
          fix: '请联系系统管理员检查此错误',
          note: '这是一个意外的处理错误，需要技术支持',
        },
        clientIp,
        request.headers.get('user-agent') || 'unknown'
      )
    } catch (recordError) {
      console.error('记录验证失败详情时出错:', recordError)
    }

    // 记录错误日志
    try {
      await prisma.systemLog.create({
        data: {
          userId: null,
          action: 'WEBHOOK_ERROR',
          resource: 'WebhookData',
          resourceId: null,
          details: {
            formId,
            error: error instanceof Error ? error.message : 'Unknown error',
            clientIp,
          },
          ipAddress: clientIp,
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      })
    } catch (logError) {
      console.error('记录错误日志失败:', logError)
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Webhook processing failed',
      },
      { status: 500 }
    )
  }
}

// GET /api/webhook/[formId] - 获取Webhook信息（用于测试）
export async function GET(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const formId = params.formId

    // 查找表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId },
      select: {
        formId: true,
        formName: true,
        webhookUrl: true,
        tableName: true,
        fieldCount: true,
        isActive: true,
        createdAt: true,
      },
    })

    if (!formConfig) {
      return NextResponse.json(
        {
          success: false,
          error: 'Form configuration not found',
        },
        { status: 404 }
      )
    }

    // 获取最近的数据统计
    const recentDataCount = await prisma.$queryRawUnsafe<
      Array<{ count: bigint }>
    >(
      `SELECT COUNT(*) as count FROM \`${formConfig.tableName}\`
       WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)`
    )

    const totalDataCount = await prisma.$queryRawUnsafe<
      Array<{ count: bigint }>
    >(`SELECT COUNT(*) as count FROM \`${formConfig.tableName}\``)

    return NextResponse.json({
      success: true,
      data: {
        ...formConfig,
        stats: {
          totalRecords: Number(totalDataCount[0]?.count || 0),
          recentRecords: Number(recentDataCount[0]?.count || 0),
        },
      },
    })
  } catch (error) {
    console.error('获取Webhook信息失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get webhook info',
      },
      { status: 500 }
    )
  }
}
